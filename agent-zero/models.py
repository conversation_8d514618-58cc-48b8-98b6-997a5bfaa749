
# REIGN Integration - Direct Neural Network Communication
import asyncio
from reign_integration_bridge import ReignNeuralInterface

class ReignModel:
    def __init__(self):
        self.reign = ReignNeuralInterface()
        self.reign.start_reign_core()
    
    async def chat_completion(self, messages, **kwargs):
        """Replace API calls with REIGN neural network."""
        # Extract the last user message
        user_message = ""
        for msg in messages:
            if msg.get('role') == 'user':
                user_message = msg.get('content', '')
        
        # Query REIGN directly
        response = await self.reign.query_reign(user_message, {'messages': messages})
        
        # Format response like OpenAI API
        return {
            'choices': [{
                'message': {
                    'role': 'assistant',
                    'content': response
                }
            }]
        }

# Replace all model instances with REIGN
reign_model = ReignModel()

def get_model(model_name=None):
    return reign_model
