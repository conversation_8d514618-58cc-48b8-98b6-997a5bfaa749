/* Modal Styles */

/* Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2001;
}

/* Modal Container */
.modal-container {
  background-color: var(--color-panel);
  border-radius: 12px;
  width: 1100px; /* Reduced from 1200px to 1100px */
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 4px 23px rgba(0, 0, 0, 0.2);
  box-sizing: border-box;
}

.light-mode .modal-container {
  background-color: var(--color-panel-light);
}

/* Mobile Viewport Behavior */
@media (max-width: 1280px) {
  .modal-container {
    width: 95%; /* Take up most of the screen on mobile */
    min-width: unset; /* Remove min-width constraints */
    max-width: 95%; /* Ensure consistent width */
  }

  /* Ensure section content can scroll horizontally */
  .section {
    overflow-x: auto;
  }
}

/* Modal Header */
.modal-header {
  display: grid;
  grid-template-columns: 40fr 0.5fr;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1.5rem 0.5rem 2rem;
  background-color: var(--color-background);
  color: var(--color-primary);
  border-bottom: 1px solid var(--color-border);
}

.modal-header h2 {
  font-size: var(--font-size-large);
  margin: 0;
}

/* Modal Subheader */
.modal-subheader {
  display: inline;
  justify-content: space-between;
  align-items: center;
  padding: 0.7rem 1.5rem;
}

/* Modal Close Button */
.modal-close {
  background: none;
  border: none;
  font-size: xx-large;
  color: var(--color-text);
  opacity: 0.7;
  cursor: pointer;
  padding: 0;
  transition: opacity 0.2s;
}

.modal-close:hover {
  opacity: 1;
}

/* Modal Description */
.modal-description {
  padding: 0.8rem 1rem 0 1rem;
  flex-grow: 1;
}

/* Modal Content */
.modal-content {
  padding: 0.5rem 1.5rem 0 1.5rem;
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(90vh);
  flex-grow: 1;
  background-clip: border-box;
  border: 6px solid transparent;
  margin-bottom: 0;
  padding-bottom: 10px;
  box-sizing: border-box;
}

.modal-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.modal-content::-webkit-scrollbar-track {
  background: transparent;
  margin: 4px 0;
  border-radius: 6px;
}

.modal-content::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.7);
}

/* Full Screen Input Modal Styles */
.full-screen-input-modal {
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  position: relative;
  padding: 0;
  background-color: rgb(20, 20, 20, 0.96);
  border: 1.5px solid var(--color-border);
}

.full-screen-input-modal h2 {
  margin: 0;
  padding: 0;
  font-size: 1.1rem;
  color: var(--color-text);
  opacity: 0.8;
}

.full-screen-input-modal .modal-close {
  position: absolute;
  top: 1.2rem;
  right: 1rem;
  font-size: 1.5rem;
  padding: 0 0.5rem;
  line-height: 0.8;
}

.full-screen-input-modal .btn-ok {
  margin-right: 1rem;
}

.full-screen-input-modal .modal-content {
  height: calc(80vh);
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.full-screen-input-modal .modal-footer {
  background: transparent;
  max-height: 50px;
}

#full-screen-input {
  width: 100%;
  height: calc(100% - 50px);
  border: none;
  background-color: transparent;
  color: var(--color-text);
  font-family: "Roboto Mono", monospace;
  font-optical-sizing: auto;
  font-size: 0.955rem;
  padding: 1.2rem 1rem;
  resize: none;
  outline: none;
}

#full-screen-input::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

#full-screen-input::-webkit-scrollbar-track {
  background: transparent;
  margin: 14px;
  border-radius: 6px;
}

#full-screen-input::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 6px;
  -webkit-transition: background-color 0.2s ease;
  transition: background-color 0.2s ease;
}

#full-screen-input::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.7);
}

.light-mode .full-screen-input-modal {
  background-color: rgb(220, 220, 220, 0.86);
}

.full-screen-input-modal .modal-footer {
  padding: 1rem 0;
  border-top: none;
  background: transparent;
}

/* Buttons Container */
#buttons-container {
  display: flex;
  gap: 0.875rem !important;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0.6rem 2rem 0.6rem 0;
  border-top: 1px solid var(--color-border);
  background: var(--color-background);
  gap: 1rem;
}

/* Section Styles */
.section {
  margin-bottom: 2rem;
  padding: 1rem;
  padding-bottom: 0;
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: var(--color-primary);
  margin-bottom: 0.5rem;
}

.section-description {
  color: var(--color-text);
  margin-bottom: 1rem;
}

/* Button Styles */
.btn {
  font-weight: 500;
  padding: 0.5rem 1.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  border: none;
  font-size: 0.875rem;
  font-family: "Rubik", Arial, Helvetica, sans-serif;
}

.btn.slim {
  padding: 0.2em 0.4em;
}

.btn.primary {
  background: #2196f3;
  color: white;
  width: fit-content;
}

.btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.btn-ok {
  background: #4248f1;
  color: white;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: background 0.3s ease-in-out;
}

.btn-ok > svg {
  max-width: 20px;
}

.btn-ok:hover {
  background: #353bc5;
}

.btn-ok:active {
  background: #2b309c;
}

.btn-cancel {
  background: transparent;
  color: var(--color-accent);
  border: 0.15rem solid var(--color-accent);
  transition: background 0.3s ease-in-out, color 0.3s ease-in-out;
}

.btn-cancel:hover {
  background: var(--color-accent);
  color: var(--color-text);
}

.btn-cancel:active {
  background: #a94658;
  color: var(--color-text);
}

.light-mode .btn-cancel:hover {
  background: var(--color-accent);
  color: var(--color-background);
}

.light-mode .btn-cancel:active {
  background: #a94658;
  color: var(--color-background);
}

.btn-field {
  background: #2196f3;
  color: white;
  width: fit-content;
}

.btn-field:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Typography */
h2 {
  color: var(--color-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-header {
    padding-left: 1.1rem;
    text-wrap: nowrap;
  }

  .modal-content {
    padding: 0.5rem;
    overflow-y: auto;
    flex-grow: 1;
  }

  .modal-footer {
    padding: var(--spacing-sm) 0 var(--spacing-sm) 0 !important;
  }

  .section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    padding-bottom: 0;
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
  }

  #buttons-container {
    margin: 0 auto;
  }

  .btn {
    padding: 0.5rem 1.7rem;
  }
}

@media (max-width: 540px) {
  .modal-header h2 {
    font-size: var(--font-size-normal);
    margin: 0;
  }

  #buttons-container {
    max-height: 50px;
  }

  .btn {
    text-wrap: wrap;
    font-size: var(--font-size-small);
  }

  .btn-upload {
    margin: 0 auto;
    gap: 0.5rem;
    align-items: center;
  }

  .btn-upload > svg {
    width: 20px;
  }
}

/* Editor Toolbar */
.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  background-color: rgba(30, 30, 30, 0.95);
  border-bottom: 1px solid var(--color-border);
}

.light-mode .editor-toolbar {
  background-color: rgba(240, 240, 240, 0.95);
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0 0.5rem;
  border-right: 1px solid var(--color-border);
}

.toolbar-group:last-child {
  border-right: none;
}

.toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  padding: 0.4rem;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 4px;
  color: var(--color-text);
  opacity: 0.7;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toolbar-button svg {
  width: 18px;
  height: 18px;
}

.toolbar-button:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
}

.toolbar-button:active {
  transform: translateY(1px);
}

.toolbar-button.active {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: var(--color-border);
  opacity: 1;
}

.toolbar-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.toolbar-button:disabled:hover {
  background-color: transparent;
}
