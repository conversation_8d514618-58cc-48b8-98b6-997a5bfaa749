#!/bin/bash
# UNIFIED SOVEREIGN-AI Deployment Script
# Deploy the ultimate AI interface with all components integrated

echo "🔥🔥🔥 UNIFIED SOVEREIGN-AI DEPLOYMENT 🔥🔥🔥"
echo "=================================================="
echo "🧠 REIGN Neural Core (Project Obsoletion)"
echo "💬 SillyTavern (Base Platform)"
echo "💻 Code Companion (Integrated Coding AI)"
echo "🤖 Agent Zero (Integrated OS Control)"
echo "🌐 Browser Integration (Built-in)"
echo "=================================================="
echo ""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[ℹ]${NC} $1"
}

print_step() {
    echo -e "${PURPLE}[🚀]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "unified_sovereign_ai.py" ]; then
    print_error "Unified SOVEREIGN-AI files not found. Please run from the project directory."
    exit 1
fi

print_step "Checking UNIFIED SOVEREIGN-AI components..."

# Check all components
components_found=0

if [ -f "obsoletion_enhanced.efi" ] || [ -f "obsoletion.efi" ]; then
    print_status "REIGN neural core found"
    ((components_found++))
else
    print_warning "REIGN neural core not found"
fi

if [ -d "silly-tavern" ] && [ -f "silly-tavern/server.js" ]; then
    print_status "SillyTavern found"
    ((components_found++))
else
    print_warning "SillyTavern not found"
fi

if [ -d "agent-zero" ] && [ -f "agent-zero/agent.py" ]; then
    print_status "Agent Zero found"
    ((components_found++))
else
    print_warning "Agent Zero not found"
fi

if [ -d "code-companion" ]; then
    print_status "Code Companion found"
    ((components_found++))
else
    print_warning "Code Companion not found"
fi

echo ""
print_info "Found $components_found/4 components"

if [ $components_found -lt 2 ]; then
    print_error "Insufficient components for unified deployment"
    print_info "Please ensure you have at least REIGN and SillyTavern"
    exit 1
fi

echo ""
print_step "Checking system dependencies..."

# Check Python
if command -v python3 &> /dev/null; then
    print_status "Python 3 found"
else
    print_error "Python 3 not found. Please install Python 3."
    exit 1
fi

# Check Node.js
if command -v node &> /dev/null; then
    print_status "Node.js found"
else
    print_warning "Node.js not found. SillyTavern requires Node.js."
    print_info "Install with: sudo apt install nodejs npm"
fi

# Check required Python packages
print_info "Checking Python packages..."
python3 -c "import flask, tkinter" 2>/dev/null
if [ $? -eq 0 ]; then
    print_status "Required Python packages found"
else
    print_warning "Installing required Python packages..."
    pip3 install flask requests websocket-client python-socketio
fi

echo ""
print_step "UNIFIED SOVEREIGN-AI Deployment Options:"
echo ""
echo "1. 🚀 Full Unified Deployment (Recommended)"
echo "2. 🧠 REIGN + SillyTavern Only"
echo "3. 💻 Add Code Companion Integration"
echo "4. 🤖 Add Agent Zero Integration"
echo "5. 🌐 Add Browser Integration"
echo "6. 🔧 Setup All Integrations"
echo "7. 🎮 Launch Unified Interface"
echo "8. ❌ Exit"
echo ""

read -p "Choose deployment option (1-8): " choice

case $choice in
    1)
        print_step "Deploying Full Unified SOVEREIGN-AI..."
        echo ""
        
        print_info "Step 1: Preparing integration environment..."
        
        # Create integration directories
        mkdir -p integrations/extensions
        mkdir -p integrations/bridges
        
        print_info "Step 2: Setting up component integrations..."
        
        # Remove Docker dependencies from Agent Zero
        if [ -d "agent-zero" ]; then
            print_info "Removing Docker dependencies from Agent Zero..."
            python3 remove_docker_dependencies.py
        fi
        
        print_info "Step 3: Creating integration bridges..."
        
        # Start the integration bridge
        python3 reign_integration_bridge.py &
        BRIDGE_PID=$!
        
        sleep 2
        
        print_info "Step 4: Launching Unified SOVEREIGN-AI Interface..."
        
        # Launch the unified interface
        python3 unified_sovereign_ai.py
        
        # Cleanup
        kill $BRIDGE_PID 2>/dev/null
        ;;
        
    2)
        print_step "Deploying REIGN + SillyTavern..."
        
        # Start REIGN
        python3 obsoletion_monitor.py &
        REIGN_PID=$!
        
        sleep 3
        
        # Start SillyTavern
        cd silly-tavern
        npm install
        node server.js &
        TAVERN_PID=$!
        cd ..
        
        print_status "REIGN + SillyTavern deployed!"
        print_info "SillyTavern: http://localhost:8000"
        
        read -p "Press Enter to stop..."
        kill $REIGN_PID $TAVERN_PID 2>/dev/null
        ;;
        
    3)
        print_step "Adding Code Companion Integration..."
        python3 -c "
from unified_sovereign_ai import UnifiedSovereignAI
app = UnifiedSovereignAI()
app.integrate_code_companion()
print('Code Companion integration complete!')
"
        ;;
        
    4)
        print_step "Adding Agent Zero Integration..."
        python3 -c "
from unified_sovereign_ai import UnifiedSovereignAI
app = UnifiedSovereignAI()
app.integrate_agent_zero()
print('Agent Zero integration complete!')
"
        ;;
        
    5)
        print_step "Adding Browser Integration..."
        python3 -c "
from unified_sovereign_ai import UnifiedSovereignAI
app = UnifiedSovereignAI()
app.setup_browser_integration()
print('Browser integration complete!')
"
        ;;
        
    6)
        print_step "Setting up all integrations..."
        
        print_info "Creating Code Companion integration..."
        python3 -c "
from unified_sovereign_ai import UnifiedSovereignAI
app = UnifiedSovereignAI()
app.create_code_companion_bridge()
"
        
        print_info "Creating Agent Zero integration..."
        python3 -c "
from unified_sovereign_ai import UnifiedSovereignAI
app = UnifiedSovereignAI()
app.create_agent_zero_bridge()
"
        
        print_info "Setting up browser integration..."
        python3 -c "
from unified_sovereign_ai import UnifiedSovereignAI
app = UnifiedSovereignAI()
app.setup_browser_integration()
"
        
        print_status "All integrations setup complete!"
        ;;
        
    7)
        print_step "Launching Unified SOVEREIGN-AI Interface..."
        python3 unified_sovereign_ai.py
        ;;
        
    8)
        print_info "Exiting deployment."
        exit 0
        ;;
        
    *)
        print_error "Invalid option. Please choose 1-8."
        exit 1
        ;;
esac

echo ""
print_step "UNIFIED SOVEREIGN-AI Deployment Complete!"
echo ""
print_status "🎉 Welcome to the ultimate AI experience!"
echo ""
echo -e "${CYAN}Your Unified SOVEREIGN-AI Features:${NC}"
echo ""
echo -e "${CYAN}🧠 REIGN Neural Core${NC} - Firmware-level AI with 0.19ms inference"
echo -e "${CYAN}💬 SillyTavern Base${NC} - Advanced chat interface with roleplay"
echo -e "${CYAN}💻 Code Companion${NC} - Integrated AI coding assistance"
echo -e "${CYAN}🤖 Agent Zero${NC} - Multi-agent OS control and automation"
echo -e "${CYAN}🌐 Browser Integration${NC} - Built-in web navigation and analysis"
echo ""
print_info "All components work together seamlessly in one interface!"
print_info "Access via the unified interface or individual components."
echo ""
print_status "🚀 UNIFIED SOVEREIGN-AI: The future of AI is here!"
