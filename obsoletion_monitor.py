#!/usr/bin/env python3
"""
Project Obsoletion - Real-time AI Monitor
Monitors the AI LLM output in real-time with visual feedback
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import subprocess
import threading
import time
import re
from collections import deque

class ObsoletionMonitor:
    def __init__(self, root):
        self.root = root
        self.root.title("Project Obsoletion - AI LLM Monitor")
        self.root.geometry("1200x800")
        self.root.configure(bg='#0a0a0a')
        
        self.process = None
        self.monitoring = False
        self.output_buffer = deque(maxlen=1000)
        self.stats = {
            'total_chars': 0,
            'korean_chars': 0,
            'lines': 0,
            'start_time': None
        }
        
        self.setup_ui()
    
    def setup_ui(self):
        # Title
        title_frame = tk.Frame(self.root, bg='#0a0a0a')
        title_frame.pack(pady=10)
        
        title_label = tk.Label(title_frame, 
                              text="PROJECT OBSOLETION AI MONITOR", 
                              font=('Courier', 20, 'bold'),
                              fg='#00ff00', bg='#0a0a0a')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame,
                                 text="Real-time Neural Network Output Monitoring",
                                 font=('Courier', 10),
                                 fg='#ffffff', bg='#0a0a0a')
        subtitle_label.pack()
        
        # Control frame
        control_frame = tk.Frame(self.root, bg='#1a1a1a', relief='raised', bd=2)
        control_frame.pack(fill='x', padx=10, pady=5)
        
        self.start_btn = tk.Button(control_frame, text="🚀 START AI LLM", 
                                  command=self.start_monitoring,
                                  bg='#00aa00', fg='#ffffff', font=('Arial', 12, 'bold'))
        self.start_btn.pack(side='left', padx=5, pady=5)
        
        self.stop_btn = tk.Button(control_frame, text="⏹️ STOP AI", 
                                 command=self.stop_monitoring,
                                 bg='#aa0000', fg='#ffffff', font=('Arial', 12, 'bold'),
                                 state='disabled')
        self.stop_btn.pack(side='left', padx=5, pady=5)
        
        self.clear_btn = tk.Button(control_frame, text="🗑️ CLEAR", 
                                  command=self.clear_output,
                                  bg='#666666', fg='#ffffff', font=('Arial', 10))
        self.clear_btn.pack(side='left', padx=5, pady=5)
        
        # Status frame
        status_frame = tk.Frame(self.root, bg='#2a2a2a', relief='sunken', bd=2)
        status_frame.pack(fill='x', padx=10, pady=5)
        
        self.status_label = tk.Label(status_frame,
                                    text="Status: Ready to monitor AI LLM",
                                    font=('Courier', 10),
                                    fg='#00ff00', bg='#2a2a2a')
        self.status_label.pack(side='left', pady=5)
        
        self.stats_label = tk.Label(status_frame,
                                   text="Stats: 0 chars | 0 Korean | 0 lines",
                                   font=('Courier', 10),
                                   fg='#ffff00', bg='#2a2a2a')
        self.stats_label.pack(side='right', pady=5)
        
        # Main content frame
        content_frame = tk.Frame(self.root, bg='#0a0a0a')
        content_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Output display
        output_frame = tk.LabelFrame(content_frame, text="AI LLM Output Stream",
                                   font=('Courier', 12, 'bold'),
                                   fg='#00ff00', bg='#0a0a0a')
        output_frame.pack(fill='both', expand=True, side='left', padx=(0, 5))
        
        self.output_text = scrolledtext.ScrolledText(output_frame,
                                                    bg='#000000', fg='#00ff00',
                                                    font=('Courier', 9),
                                                    wrap='word',
                                                    state='disabled')
        self.output_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Analysis panel
        analysis_frame = tk.LabelFrame(content_frame, text="Real-time Analysis",
                                     font=('Courier', 12, 'bold'),
                                     fg='#ffff00', bg='#0a0a0a')
        analysis_frame.pack(fill='y', side='right', padx=(5, 0))
        analysis_frame.configure(width=300)
        
        self.analysis_text = scrolledtext.ScrolledText(analysis_frame,
                                                      bg='#1a1a1a', fg='#ffff00',
                                                      font=('Courier', 8),
                                                      wrap='word',
                                                      state='disabled',
                                                      width=35, height=20)
        self.analysis_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Initialize analysis
        self.update_analysis("AI Monitor initialized.\nWaiting for neural network activation...")
    
    def start_monitoring(self):
        """Start monitoring the AI LLM output."""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.stats['start_time'] = time.time()
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.status_label.config(text="Status: Starting AI LLM system...", fg='#ffff00')
        
        # Start the QEMU process in a separate thread
        thread = threading.Thread(target=self.run_obsoletion)
        thread.daemon = True
        thread.start()
    
    def stop_monitoring(self):
        """Stop monitoring and terminate the AI process."""
        self.monitoring = False
        if self.process:
            self.process.terminate()
            self.process = None
        
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.status_label.config(text="Status: AI LLM stopped", fg='#ff6600')
        self.update_analysis("AI monitoring stopped.")
    
    def clear_output(self):
        """Clear the output display."""
        self.output_text.config(state='normal')
        self.output_text.delete(1.0, tk.END)
        self.output_text.config(state='disabled')
        
        self.analysis_text.config(state='normal')
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.config(state='disabled')
        
        self.output_buffer.clear()
        self.stats = {
            'total_chars': 0,
            'korean_chars': 0,
            'lines': 0,
            'start_time': None
        }
        self.update_stats()
    
    def run_obsoletion(self):
        """Run the Project Obsoletion AI LLM system."""
        try:
            # Create startup script
            with open('startup.nsh', 'w') as f:
                f.write('''cls
echo ======================================================================
echo                    PROJECT OBSOLETION AI MONITOR
echo                     Neural Network Output Stream
echo ======================================================================
echo.
echo [OBSOLETION] Initializing AI LLM system...
echo [OBSOLETION] Loading neural network parameters...
echo [OBSOLETION] Starting inference engine...
echo.
if exist obsoletion_enhanced.efi then
    obsoletion_enhanced.efi
endif
if exist obsoletion.efi then
    obsoletion.efi
endif
echo.
echo [OBSOLETION] AI session completed.
''')
            
            # Start QEMU process
            cmd = [
                'qemu-system-x86_64',
                '-bios', '/usr/share/ovmf/OVMF.fd',
                '-drive', 'format=raw,file=fat:rw:.',
                '-m', '2048',
                '-smp', '2',
                '-boot', 'order=c',
                '-boot', 'menu=off',
                '-serial', 'stdio',
                '-monitor', 'none',
                '-nographic',
                '-no-reboot'
            ]
            
            self.process = subprocess.Popen(cmd, stdout=subprocess.PIPE, 
                                          stderr=subprocess.STDOUT, 
                                          universal_newlines=True, 
                                          bufsize=1)
            
            self.status_label.config(text="Status: AI LLM active - monitoring output...", fg='#00ff00')
            
            # Read output in real-time
            for line in iter(self.process.stdout.readline, ''):
                if not self.monitoring:
                    break
                
                self.process_output_line(line)
            
            self.process.wait()
            
        except Exception as e:
            self.update_analysis(f"Error starting AI: {e}")
        finally:
            if self.monitoring:
                self.stop_monitoring()
    
    def process_output_line(self, line):
        """Process a line of output from the AI system."""
        if not line.strip():
            return
        
        # Update statistics
        self.stats['total_chars'] += len(line)
        self.stats['korean_chars'] += len(re.findall(r'[가-힣꾯]', line))
        self.stats['lines'] += 1
        
        # Add to buffer
        self.output_buffer.append(line.rstrip())
        
        # Update display
        self.root.after(0, self.update_display, line)
        self.root.after(0, self.update_stats)
        
        # Analyze output
        if '꾯' in line:
            self.root.after(0, self.update_analysis, "🧠 Neural network generating Korean tokens!")
        elif 'OBSOLETION' in line:
            self.root.after(0, self.update_analysis, "🚀 Project Obsoletion system message detected")
        elif len(line) > 100:
            self.root.after(0, self.update_analysis, f"📊 Long output detected: {len(line)} chars")
    
    def update_display(self, line):
        """Update the output display with new line."""
        self.output_text.config(state='normal')
        self.output_text.insert(tk.END, line)
        self.output_text.see(tk.END)
        self.output_text.config(state='disabled')
    
    def update_stats(self):
        """Update the statistics display."""
        runtime = 0
        if self.stats['start_time']:
            runtime = time.time() - self.stats['start_time']
        
        stats_text = (f"Stats: {self.stats['total_chars']} chars | "
                     f"{self.stats['korean_chars']} Korean | "
                     f"{self.stats['lines']} lines | "
                     f"{runtime:.1f}s")
        
        self.stats_label.config(text=stats_text)
    
    def update_analysis(self, message):
        """Update the analysis panel with new information."""
        timestamp = time.strftime("%H:%M:%S")
        
        self.analysis_text.config(state='normal')
        self.analysis_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.analysis_text.see(tk.END)
        self.analysis_text.config(state='disabled')

def main():
    root = tk.Tk()
    app = ObsoletionMonitor(root)
    root.mainloop()

if __name__ == "__main__":
    main()
