#!/usr/bin/env python3
"""
SOVEREIGN-AI: Master Integration System
Unifying REIGN (Project Obsoletion), Agent Zero, Code Companion, and Silly<PERSON>avern
into the ultimate AI empire!
"""

import os
import sys
import subprocess
import threading
import time
import json
import asyncio
from pathlib import Path
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox

class SovereignAI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("SOVEREIGN-AI: The Ultimate AI Empire")
        self.root.geometry("1400x900")
        self.root.configure(bg='#0a0a0a')
        
        # Core components
        self.reign_process = None
        self.agent_zero_process = None
        self.silly_tavern_process = None
        self.code_companion_active = False
        
        # Status tracking
        self.component_status = {
            'REIGN': 'Offline',
            'Agent Zero': 'Offline', 
            'Code Companion': 'Offline',
            'SillyTavern': 'Offline'
        }
        
        self.setup_ui()
        self.check_dependencies()
    
    def setup_ui(self):
        """Setup the SOVEREIGN-AI control interface."""
        # Title and header
        header_frame = tk.Frame(self.root, bg='#0a0a0a')
        header_frame.pack(fill='x', pady=10)
        
        title_label = tk.Label(header_frame, 
                              text="SOVEREIGN-AI EMPIRE", 
                              font=('Courier', 24, 'bold'),
                              fg='#ff6600', bg='#0a0a0a')
        title_label.pack()
        
        subtitle_label = tk.Label(header_frame,
                                 text="REIGN Neural Core + Agent Zero + Code Companion + SillyTavern",
                                 font=('Courier', 12),
                                 fg='#ffffff', bg='#0a0a0a')
        subtitle_label.pack()
        
        # Main control panel
        control_frame = tk.Frame(self.root, bg='#1a1a1a', relief='raised', bd=3)
        control_frame.pack(fill='x', padx=10, pady=10)
        
        # Component controls
        components_frame = tk.LabelFrame(control_frame, text="AI Component Control",
                                       font=('Courier', 14, 'bold'),
                                       fg='#00ff00', bg='#1a1a1a')
        components_frame.pack(fill='x', padx=10, pady=10)
        
        # REIGN Controls
        reign_frame = tk.Frame(components_frame, bg='#1a1a1a')
        reign_frame.pack(fill='x', pady=5)
        
        tk.Label(reign_frame, text="REIGN (Neural Core):", 
                font=('Courier', 12, 'bold'), fg='#ff6600', bg='#1a1a1a').pack(side='left')
        
        self.reign_status = tk.Label(reign_frame, text="Offline", 
                                   font=('Courier', 10), fg='#ff0000', bg='#1a1a1a')
        self.reign_status.pack(side='left', padx=10)
        
        self.reign_start_btn = tk.Button(reign_frame, text="🧠 START REIGN", 
                                       command=self.start_reign,
                                       bg='#ff6600', fg='#ffffff', font=('Arial', 10, 'bold'))
        self.reign_start_btn.pack(side='right', padx=5)
        
        # Agent Zero Controls
        agent_frame = tk.Frame(components_frame, bg='#1a1a1a')
        agent_frame.pack(fill='x', pady=5)
        
        tk.Label(agent_frame, text="Agent Zero (Multi-Agent):", 
                font=('Courier', 12, 'bold'), fg='#00ff00', bg='#1a1a1a').pack(side='left')
        
        self.agent_status = tk.Label(agent_frame, text="Offline", 
                                   font=('Courier', 10), fg='#ff0000', bg='#1a1a1a')
        self.agent_status.pack(side='left', padx=10)
        
        self.agent_start_btn = tk.Button(agent_frame, text="🤖 START AGENTS", 
                                       command=self.start_agent_zero,
                                       bg='#00aa00', fg='#ffffff', font=('Arial', 10, 'bold'))
        self.agent_start_btn.pack(side='right', padx=5)
        
        # Code Companion Controls
        code_frame = tk.Frame(components_frame, bg='#1a1a1a')
        code_frame.pack(fill='x', pady=5)
        
        tk.Label(code_frame, text="Code Companion (Coding AI):", 
                font=('Courier', 12, 'bold'), fg='#0066ff', bg='#1a1a1a').pack(side='left')
        
        self.code_status = tk.Label(code_frame, text="Offline", 
                                  font=('Courier', 10), fg='#ff0000', bg='#1a1a1a')
        self.code_status.pack(side='left', padx=10)
        
        self.code_start_btn = tk.Button(code_frame, text="💻 START CODING", 
                                      command=self.start_code_companion,
                                      bg='#0066aa', fg='#ffffff', font=('Arial', 10, 'bold'))
        self.code_start_btn.pack(side='right', padx=5)
        
        # SillyTavern Controls
        tavern_frame = tk.Frame(components_frame, bg='#1a1a1a')
        tavern_frame.pack(fill='x', pady=5)
        
        tk.Label(tavern_frame, text="SillyTavern (Chat Interface):", 
                font=('Courier', 12, 'bold'), fg='#ff00ff', bg='#1a1a1a').pack(side='left')
        
        self.tavern_status = tk.Label(tavern_frame, text="Offline", 
                                    font=('Courier', 10), fg='#ff0000', bg='#1a1a1a')
        self.tavern_status.pack(side='left', padx=10)
        
        self.tavern_start_btn = tk.Button(tavern_frame, text="💬 START CHAT", 
                                        command=self.start_silly_tavern,
                                        bg='#aa00aa', fg='#ffffff', font=('Arial', 10, 'bold'))
        self.tavern_start_btn.pack(side='right', padx=5)
        
        # Master controls
        master_frame = tk.Frame(control_frame, bg='#1a1a1a')
        master_frame.pack(fill='x', pady=10)
        
        self.start_all_btn = tk.Button(master_frame, text="🚀 START SOVEREIGN-AI EMPIRE", 
                                     command=self.start_all_systems,
                                     bg='#ff0000', fg='#ffffff', font=('Arial', 14, 'bold'))
        self.start_all_btn.pack(side='left', padx=10)
        
        self.stop_all_btn = tk.Button(master_frame, text="⏹️ STOP ALL SYSTEMS", 
                                    command=self.stop_all_systems,
                                    bg='#666666', fg='#ffffff', font=('Arial', 14, 'bold'))
        self.stop_all_btn.pack(side='right', padx=10)
        
        # Status and logs
        status_frame = tk.LabelFrame(self.root, text="System Status & Logs",
                                   font=('Courier', 14, 'bold'),
                                   fg='#ffff00', bg='#0a0a0a')
        status_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.status_display = scrolledtext.ScrolledText(status_frame,
                                                       bg='#000000', fg='#00ff00',
                                                       font=('Courier', 10),
                                                       wrap='word')
        self.status_display.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Quick access buttons
        quick_frame = tk.Frame(self.root, bg='#2a2a2a', relief='raised', bd=2)
        quick_frame.pack(fill='x', padx=10, pady=5)
        
        quick_buttons = [
            ("🌐 Open Agent Zero UI", self.open_agent_zero_ui),
            ("💬 Open SillyTavern", self.open_silly_tavern_ui),
            ("🧠 REIGN Monitor", self.open_reign_monitor),
            ("⚙️ System Config", self.open_system_config)
        ]
        
        for text, command in quick_buttons:
            btn = tk.Button(quick_frame, text=text, command=command,
                           bg='#333333', fg='#ffffff', font=('Arial', 10))
            btn.pack(side='left', padx=5, pady=5)
        
        # Initialize status
        self.log_status("SOVEREIGN-AI Control System initialized.")
        self.log_status("Ready to deploy the ultimate AI empire!")
    
    def log_status(self, message):
        """Log a status message."""
        timestamp = time.strftime("%H:%M:%S")
        self.status_display.insert(tk.END, f"[{timestamp}] {message}\n")
        self.status_display.see(tk.END)
        self.root.update()
    
    def check_dependencies(self):
        """Check if all required components are available."""
        self.log_status("Checking SOVEREIGN-AI dependencies...")
        
        # Check REIGN (Project Obsoletion)
        if os.path.exists("obsoletion_enhanced.efi") or os.path.exists("obsoletion.efi"):
            self.log_status("✓ REIGN neural core found")
        else:
            self.log_status("⚠ REIGN neural core not found - building required")
        
        # Check Agent Zero
        if os.path.exists("agent-zero/agent.py"):
            self.log_status("✓ Agent Zero framework found")
        else:
            self.log_status("✗ Agent Zero not found")
        
        # Check Code Companion
        if os.path.exists("code-companion/lua"):
            self.log_status("✓ Code Companion found")
        else:
            self.log_status("✗ Code Companion not found")
        
        # Check SillyTavern
        if os.path.exists("silly-tavern/server.js"):
            self.log_status("✓ SillyTavern found")
        else:
            self.log_status("✗ SillyTavern not found")
        
        self.log_status("Dependency check complete.")
    
    def start_reign(self):
        """Start the REIGN neural core."""
        self.log_status("🧠 Starting REIGN neural core...")
        
        try:
            # Use the existing obsoletion monitor
            self.reign_process = subprocess.Popen([
                'python3', 'obsoletion_monitor.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            self.reign_status.config(text="Online", fg='#00ff00')
            self.component_status['REIGN'] = 'Online'
            self.log_status("✓ REIGN neural core started successfully!")
            
        except Exception as e:
            self.log_status(f"✗ Failed to start REIGN: {e}")
    
    def start_agent_zero(self):
        """Start Agent Zero framework."""
        self.log_status("🤖 Starting Agent Zero multi-agent framework...")
        
        try:
            # Start Agent Zero UI
            os.chdir("agent-zero")
            self.agent_zero_process = subprocess.Popen([
                'python3', 'run_ui.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            os.chdir("..")
            
            self.agent_status.config(text="Online", fg='#00ff00')
            self.component_status['Agent Zero'] = 'Online'
            self.log_status("✓ Agent Zero framework started successfully!")
            
        except Exception as e:
            self.log_status(f"✗ Failed to start Agent Zero: {e}")
    
    def start_code_companion(self):
        """Start Code Companion integration."""
        self.log_status("💻 Activating Code Companion AI coding assistant...")
        
        try:
            # Code Companion is a Neovim plugin, so we'll create a bridge
            self.code_companion_active = True
            self.code_status.config(text="Ready", fg='#00ff00')
            self.component_status['Code Companion'] = 'Ready'
            self.log_status("✓ Code Companion ready for integration!")
            
        except Exception as e:
            self.log_status(f"✗ Failed to activate Code Companion: {e}")
    
    def start_silly_tavern(self):
        """Start SillyTavern chat interface."""
        self.log_status("💬 Starting SillyTavern chat interface...")
        
        try:
            # Start SillyTavern server
            os.chdir("silly-tavern")
            self.silly_tavern_process = subprocess.Popen([
                'node', 'server.js'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            os.chdir("..")
            
            self.tavern_status.config(text="Online", fg='#00ff00')
            self.component_status['SillyTavern'] = 'Online'
            self.log_status("✓ SillyTavern started successfully!")
            
        except Exception as e:
            self.log_status(f"✗ Failed to start SillyTavern: {e}")
    
    def start_all_systems(self):
        """Start all SOVEREIGN-AI systems."""
        self.log_status("🚀 DEPLOYING SOVEREIGN-AI EMPIRE...")
        self.log_status("=" * 50)
        
        # Start each component
        self.start_reign()
        time.sleep(2)
        self.start_agent_zero()
        time.sleep(2)
        self.start_code_companion()
        time.sleep(1)
        self.start_silly_tavern()
        
        self.log_status("=" * 50)
        self.log_status("🎉 SOVEREIGN-AI EMPIRE DEPLOYED!")
        self.log_status("All AI systems are now operational.")
        self.log_status("REIGN is your neural core, Agent Zero handles automation,")
        self.log_status("Code Companion assists with coding, SillyTavern provides chat.")
        self.log_status("Welcome to the future of AI!")
    
    def stop_all_systems(self):
        """Stop all SOVEREIGN-AI systems."""
        self.log_status("⏹️ Shutting down SOVEREIGN-AI systems...")
        
        if self.reign_process:
            self.reign_process.terminate()
            self.reign_status.config(text="Offline", fg='#ff0000')
        
        if self.agent_zero_process:
            self.agent_zero_process.terminate()
            self.agent_status.config(text="Offline", fg='#ff0000')
        
        if self.silly_tavern_process:
            self.silly_tavern_process.terminate()
            self.tavern_status.config(text="Offline", fg='#ff0000')
        
        self.code_companion_active = False
        self.code_status.config(text="Offline", fg='#ff0000')
        
        self.log_status("✓ All systems shut down.")
    
    def open_agent_zero_ui(self):
        """Open Agent Zero web interface."""
        import webbrowser
        webbrowser.open("http://localhost:50001")
        self.log_status("🌐 Opening Agent Zero UI in browser...")
    
    def open_silly_tavern_ui(self):
        """Open SillyTavern web interface."""
        import webbrowser
        webbrowser.open("http://localhost:8000")
        self.log_status("💬 Opening SillyTavern UI in browser...")
    
    def open_reign_monitor(self):
        """Open REIGN monitoring interface."""
        subprocess.Popen(['python3', 'obsoletion_monitor.py'])
        self.log_status("🧠 Opening REIGN neural monitor...")
    
    def open_system_config(self):
        """Open system configuration."""
        messagebox.showinfo("System Config", 
                           "SOVEREIGN-AI Configuration:\n\n"
                           "• REIGN: Firmware-level neural network\n"
                           "• Agent Zero: Multi-agent automation\n"
                           "• Code Companion: AI coding assistant\n"
                           "• SillyTavern: Advanced chat interface\n\n"
                           "All systems integrated for maximum AI power!")
    
    def run(self):
        """Run the SOVEREIGN-AI control system."""
        self.root.mainloop()

def main():
    """Main entry point for SOVEREIGN-AI."""
    print("🚀 Initializing SOVEREIGN-AI Empire...")
    print("🧠 REIGN + 🤖 Agent Zero + 💻 Code Companion + 💬 SillyTavern")
    print("=" * 60)
    
    app = SovereignAI()
    app.run()

if __name__ == "__main__":
    main()
