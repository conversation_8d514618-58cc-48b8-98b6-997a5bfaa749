#!/bin/bash
# Interactive Project Obsoletion Test - See the AI in Action!

echo "╔══════════════════════════════════════════════════════════════╗"
echo "║              PROJECT OBSOLETION - INTERACTIVE TEST          ║"
echo "║           Let's see your AI LLM system in action!           ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo ""

# Configuration
OBSOLETION_EFI="obsoletion_enhanced.efi"
OVMF_PATH="/usr/share/ovmf/OVMF.fd"
OUTPUT_LOG="obsoletion_output.log"

# Function to find Project Obsoletion EFI file
find_obsoletion_efi() {
    local search_paths=(
        "obsoletion_enhanced.efi"
        "obsoletion.efi"
        "obsoletion/source/obsoletion_enhanced.efi"
        "source/obsoletion_enhanced.efi"
        "obsoletion_complete/obsoletion_enhanced.efi"
    )
    
    for path in "${search_paths[@]}"; do
        if [[ -f "$path" ]]; then
            OBSOLETION_EFI="$path"
            echo "✓ Found Project Obsoletion at: $path"
            return 0
        fi
    done
    
    echo "✗ ERROR: Project Obsoletion EFI file not found!"
    return 1
}

# Function to launch with output capture
launch_interactive() {
    echo ""
    echo "🚀 Starting Project Obsoletion AI LLM System..."
    echo "📊 Output will be captured to: $OUTPUT_LOG"
    echo "🎮 Controls:"
    echo "   - Press Ctrl+C to stop the AI system"
    echo "   - Output will be displayed in real-time"
    echo "   - Korean characters = AI is generating text!"
    echo ""
    
    # Create startup script for better interaction
    cat > startup.nsh << 'EOF'
cls
echo ======================================================================
echo                    PROJECT OBSOLETION AI LLM ACTIVE
echo                     Firmware-Level Neural Network
echo ======================================================================
echo.
echo [OBSOLETION] Initializing 50,000 parameter neural network...
echo [OBSOLETION] Loading sub-millisecond inference engine...
echo [OBSOLETION] Activating GUI interaction capabilities...
echo.
echo Starting AI LLM system...
if exist obsoletion_enhanced.efi then
    obsoletion_enhanced.efi
endif
if exist obsoletion.efi then
    obsoletion.efi
endif
echo.
echo AI LLM session completed.
EOF

    echo "Starting QEMU with Project Obsoletion..."
    echo "The AI should start generating output shortly!"
    echo ""
    
    # Launch QEMU and capture output
    qemu-system-x86_64 \
        -bios "$OVMF_PATH" \
        -drive format=raw,file=fat:rw:. \
        -m 2048 \
        -smp 2 \
        -boot order=c \
        -boot menu=off \
        -serial stdio \
        -monitor none \
        -nographic \
        -no-reboot 2>&1 | tee "$OUTPUT_LOG"
    
    echo ""
    echo "🎉 Project Obsoletion session completed!"
    echo "📄 Output saved to: $OUTPUT_LOG"
}

# Function to analyze the output
analyze_output() {
    if [[ -f "$OUTPUT_LOG" ]]; then
        echo ""
        echo "╔══════════════════════════════════════════════════════════════╗"
        echo "║                    OUTPUT ANALYSIS                          ║"
        echo "╚══════════════════════════════════════════════════════════════╝"
        echo ""
        
        local total_lines=$(wc -l < "$OUTPUT_LOG")
        local korean_chars=$(grep -o '꾯' "$OUTPUT_LOG" | wc -l)
        local file_size=$(stat -c%s "$OUTPUT_LOG")
        
        echo "📊 Session Statistics:"
        echo "   - Total output lines: $total_lines"
        echo "   - Korean characters generated: $korean_chars"
        echo "   - Output file size: $file_size bytes"
        echo ""
        
        if [[ $korean_chars -gt 100 ]]; then
            echo "✅ AI LLM is ACTIVE and generating substantial output!"
            echo "   The Korean characters indicate the neural network is working."
        elif [[ $korean_chars -gt 0 ]]; then
            echo "⚠️  AI LLM generated some output but may need optimization."
        else
            echo "❌ No AI output detected. Check the EFI application."
        fi
        
        echo ""
        echo "📄 Last 10 lines of output:"
        echo "----------------------------------------"
        tail -10 "$OUTPUT_LOG"
        echo "----------------------------------------"
        
        echo ""
        echo "💾 Full output available in: $OUTPUT_LOG"
    else
        echo "❌ No output log found."
    fi
}

# Function to launch with GUI
launch_with_gui() {
    echo ""
    echo "🖥️ Launching Project Obsoletion with GUI interface..."
    echo ""
    
    if [[ -f "launch_obsoletion_gui.py" ]]; then
        python3 launch_obsoletion_gui.py
    else
        echo "❌ GUI launcher not found. Using command line instead."
        launch_interactive
    fi
}

# Main menu
main_menu() {
    echo "Choose how you want to test Project Obsoletion:"
    echo ""
    echo "1. 🚀 Interactive Launch (Recommended)"
    echo "2. 🖥️ GUI Launch"
    echo "3. 📊 Analyze Previous Output"
    echo "4. 🔧 Quick Auto-Boot Test"
    echo "5. ❌ Exit"
    echo ""
    read -p "Enter your choice (1-5): " choice
    
    case $choice in
        1)
            launch_interactive
            analyze_output
            ;;
        2)
            launch_with_gui
            ;;
        3)
            analyze_output
            ;;
        4)
            echo "Running quick auto-boot test..."
            ./auto_boot_obsoletion.sh
            ;;
        5)
            echo "Goodbye!"
            exit 0
            ;;
        *)
            echo "Invalid choice. Please try again."
            main_menu
            ;;
    esac
}

# Main execution
main() {
    # Check prerequisites
    if ! command -v qemu-system-x86_64 &> /dev/null; then
        echo "✗ ERROR: QEMU not found. Please install: sudo apt install qemu-system-x86"
        exit 1
    fi
    
    if [[ ! -f "$OVMF_PATH" ]]; then
        echo "✗ ERROR: OVMF firmware not found. Please install: sudo apt install ovmf"
        exit 1
    fi
    
    # Find Project Obsoletion
    if ! find_obsoletion_efi; then
        exit 1
    fi
    
    # Show main menu
    main_menu
    
    echo ""
    echo "Thanks for testing Project Obsoletion!"
    echo "Your AI LLM system is working! 🎉"
}

# Run main function
main "$@"
