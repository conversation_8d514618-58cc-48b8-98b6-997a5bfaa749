#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
[TMOP] QEMU Aegis Launcher

This script provides a resilient, configurable, and self-correcting wrapper
for launching QEMU virtual machines. It embodies the Aegis Prime principles by
validating prerequisites and adaptively healing the environment if required
state (like a disk image) is missing.
"""

import os
import sys
import subprocess
import shutil

# --- [TMOP] Aegis Prime: Core Configuration & Constants ---
# Layer 0: The Genesis Forge - Defining the default reality.

class QEMUConfig:
    """A simple class to hold our QEMU configuration."""
    def __init__(self):
        # Default UEFI firmware path for Debian/Ubuntu/Kali based systems.
        self.bios_path = "/usr/share/ovmf/OVMF.fd"
        # The disk image for the VM.
        self.disk_image = "boot_disk.img"
        # Default memory in MB.
        self.memory_mb = 2048
        # Default number of CPU cores.
        self.smp_cores = 2
        # Default boot order.
        self.boot_order = "c" # 'c' for disk, 'd' for cdrom
        # Show the boot menu on startup.
        self.boot_menu = "off"  # Changed to off for automatic boot
        # Auto-boot mode for Project Obsoletion
        self.auto_boot = True
        # Project Obsoletion EFI file
        self.obsoletion_efi = "obsoletion_enhanced.efi"

def _aegis_execute(command: list, capture_output=False):
    """
    A universal guardian for executing external commands.
    Ensures commands are logged and handles errors gracefully.
    """
    print(f"[AEGIS] Executing: {' '.join(command)}")
    try:
        if capture_output:
            result = subprocess.run(command, check=True, text=True, capture_output=True)
            if result.stdout: print(f"[AEGIS] STDOUT: {result.stdout.strip()}")
            if result.stderr: print(f"[AEGIS] STDERR: {result.stderr.strip()}")
            return result
        else:
            subprocess.run(command, check=True)
            return True
    except FileNotFoundError:
        print(f"[AEGIS FAULT] Command not found: '{command[0]}'. Is the required program installed and in PATH?", file=sys.stderr)
        return None
    except subprocess.CalledProcessError as e:
        print(f"[AEGIS FAULT] Command failed with exit code {e.returncode}.", file=sys.stderr)
        if e.stdout: print(f"--- STDOUT ---\n{e.stdout}", file=sys.stderr)
        if e.stderr: print(f"--- STDERR ---\n{e.stderr}", file=sys.stderr)
        return None
    except Exception as e:
        print(f"[AEGIS FAULT] An unexpected anomaly occurred: {e}", file=sys.stderr)
        return None

# --- [TMOP] Aegis Prime: Layer 1 & 3 - Static Guardian & Adaptive Healer ---

def create_obsoletion_startup_script():
    """Create startup.nsh for automatic Project Obsoletion boot."""
    startup_content = """@echo off
echo Project Obsoletion - AI LLM Automatic Boot
echo ==========================================
echo.
echo Initializing firmware-level neural network...
echo.
# Try different possible locations for the EFI file
if exist fs0:\\obsoletion_enhanced.efi then
    echo [AEGIS] Found Project Obsoletion on fs0: - Starting AI LLM...
    fs0:\\obsoletion_enhanced.efi
endif
if exist fs1:\\obsoletion_enhanced.efi then
    echo [AEGIS] Found Project Obsoletion on fs1: - Starting AI LLM...
    fs1:\\obsoletion_enhanced.efi
endif
if exist .\\obsoletion_enhanced.efi then
    echo [AEGIS] Found Project Obsoletion in current directory - Starting AI LLM...
    .\\obsoletion_enhanced.efi
endif
if exist obsoletion_enhanced.efi then
    echo [AEGIS] Found Project Obsoletion - Starting AI LLM...
    obsoletion_enhanced.efi
endif
if exist obsoletion.efi then
    echo [AEGIS] Found Project Obsoletion (alternate) - Starting AI LLM...
    obsoletion.efi
endif
# Try EFI/BOOT directory
if exist fs0:\\EFI\\BOOT\\BOOTX64.EFI then
    echo [AEGIS] Found Project Obsoletion in EFI/BOOT - Starting AI LLM...
    fs0:\\EFI\\BOOT\\BOOTX64.EFI
endif
if exist fs1:\\EFI\\BOOT\\BOOTX64.EFI then
    echo [AEGIS] Found Project Obsoletion in EFI/BOOT - Starting AI LLM...
    fs1:\\EFI\\BOOT\\BOOTX64.EFI
endif
echo.
echo [AEGIS FAULT] Automatic boot failed - Project Obsoletion not found
echo Please manually run: obsoletion_enhanced.efi
echo.
"""

    with open("startup.nsh", "w") as f:
        f.write(startup_content)
    print("  [+] Created startup.nsh for automatic boot")

def validate_and_heal_environment(config: QEMUConfig):
    """
    Ensures all prerequisite files exist. If the disk image is missing,
    it enters an 'adaptive healing' phase to create it.
    """
    print("\n[AEGIS] [Static Guardian]: Validating execution reality...")

    # 1. Validate BIOS/Firmware
    if not os.path.exists(config.bios_path):
        print(f"[AEGIS FAULT] BIOS file not found at '{config.bios_path}'.", file=sys.stderr)
        print("Please ensure the 'ovmf' package is installed ('sudo apt install ovmf').", file=sys.stderr)
        return False
    print(f"  [+] BIOS found: {config.bios_path}")

    # 2. Validate QEMU executable
    if not shutil.which("qemu-system-x86_64"):
        print("[AEGIS FAULT] 'qemu-system-x86_64' not found.", file=sys.stderr)
        print("Please ensure QEMU is installed ('sudo apt install qemu-system-x86').", file=sys.stderr)
        return False
    print("  [+] QEMU binary found.")

    # 3. Check for Project Obsoletion EFI file
    if not os.path.exists(config.obsoletion_efi):
        print(f"  [-] Project Obsoletion EFI file '{config.obsoletion_efi}' not found.")
        # Try alternative locations
        alt_locations = [
            "obsoletion.efi",
            "obsoletion/source/obsoletion_enhanced.efi",
            "source/obsoletion_enhanced.efi"
        ]
        found = False
        for alt_path in alt_locations:
            if os.path.exists(alt_path):
                config.obsoletion_efi = alt_path
                print(f"  [+] Found Project Obsoletion at: {alt_path}")
                found = True
                break

        if not found:
            print("[AEGIS FAULT] Project Obsoletion EFI file not found in any expected location.", file=sys.stderr)
            print("Please ensure the project is built and obsoletion_enhanced.efi exists.", file=sys.stderr)
            return False
    else:
        print(f"  [+] Project Obsoletion EFI found: {config.obsoletion_efi}")

    # 4. Create startup script for automatic boot
    if config.auto_boot:
        create_obsoletion_startup_script()

    # 5. Validate Disk Image (and heal if necessary)
    if not os.path.exists(config.disk_image):
        print(f"  [-] Disk image '{config.disk_image}' not found.")
        print("[AEGIS] [Adaptive Healer]: Creating optimized boot disk for Project Obsoletion...")

        if not shutil.which("qemu-img"):
            print("[AEGIS FAULT] 'qemu-img' tool not found. Cannot create disk image.", file=sys.stderr)
            print("Please ensure QEMU is installed correctly.", file=sys.stderr)
            return False

        # Create a smaller, optimized disk for Project Obsoletion
        size_gb = 1  # 1GB is sufficient for the EFI application
        create_command = ["qemu-img", "create", "-f", "raw", config.disk_image, f"{size_gb}G"]
        if _aegis_execute(create_command):
            print(f"  [+] Healing complete. New {size_gb}GB boot disk created for Project Obsoletion.")
        else:
            print("[AEGIS FAULT] Healing failed. Could not create disk image.", file=sys.stderr)
            return False
    else:
        print(f"  [+] Disk image found: {config.disk_image}")

    print("[AEGIS] [Static Guardian]: Reality is valid and stable.")
    return True

# --- [TMOP] Aegis Prime: Layer 6 - Ethereal User Interface ---

def show_menu(config: QEMUConfig):
    """Displays the configuration menu and allows user to modify it."""
    while True:
        print("\n" + "="*15 + " Project Obsoletion QEMU Launcher " + "="*15)
        print(f" 1. Disk Image:        {config.disk_image}")
        print(f" 2. Memory (RAM):      {config.memory_mb} MB")
        print(f" 3. CPU Cores:         {config.smp_cores}")
        print(f" 4. BIOS Path:         {config.bios_path}")
        print(f" 5. Obsoletion EFI:    {config.obsoletion_efi}")
        print(f" 6. Auto-boot Mode:    {'Enabled' if config.auto_boot else 'Disabled'}")
        print(f" 7. Boot Menu:         {'On' if config.boot_menu == 'on' else 'Off'}")
        print("\n A. Auto-Launch Project Obsoletion (Recommended)")
        print(" L. Launch QEMU with current settings")
        print(" Q. Quit")
        print("="*64)

        choice = input("Enter your choice: ").strip().lower()

        if choice == '1':
            new_val = input(f"Enter new disk image path [{config.disk_image}]: ")
            if new_val: config.disk_image = new_val
        elif choice == '2':
            try:
                new_val = input(f"Enter new memory in MB [{config.memory_mb}]: ")
                if new_val: config.memory_mb = int(new_val)
            except ValueError: print("Invalid number.")
        elif choice == '3':
            try:
                new_val = input(f"Enter new CPU core count [{config.smp_cores}]: ")
                if new_val: config.smp_cores = int(new_val)
            except ValueError: print("Invalid number.")
        elif choice == '4':
            new_val = input(f"Enter new BIOS path [{config.bios_path}]: ")
            if new_val: config.bios_path = new_val
        elif choice == '5':
            new_val = input(f"Enter Project Obsoletion EFI path [{config.obsoletion_efi}]: ")
            if new_val: config.obsoletion_efi = new_val
        elif choice == '6':
            config.auto_boot = not config.auto_boot
            print(f"Auto-boot mode {'enabled' if config.auto_boot else 'disabled'}.")
        elif choice == '7':
            config.boot_menu = "off" if config.boot_menu == "on" else "on"
            print(f"Boot menu {'enabled' if config.boot_menu == 'on' else 'disabled'}.")
        elif choice == 'a':
            # Auto-launch with optimal settings for Project Obsoletion
            config.auto_boot = True
            config.boot_menu = "off"
            print("\n[AEGIS] Auto-launch mode activated - optimizing for Project Obsoletion...")
            return True
        elif choice == 'l':
            return True
        elif choice == 'q':
            return False
        else:
            print("Invalid choice. Please try again.")

def main():
    """Main execution flow."""
    config = QEMUConfig()

    if not show_menu(config):
        print("[AEGIS] Launch aborted by operator.")
        sys.exit(0)

    if not validate_and_heal_environment(config):
        print("\n[AEGIS FAULT] Environment validation failed. Aborting launch.", file=sys.stderr)
        sys.exit(1)

    # Construct the final QEMU command optimized for Project Obsoletion
    if config.auto_boot:
        # Use FAT filesystem for direct access to EFI files
        qemu_command = [
            "qemu-system-x86_64",
            "-bios", config.bios_path,
            "-drive", "format=raw,file=fat:rw:.",  # Direct filesystem access
            "-m", str(config.memory_mb),
            "-smp", str(config.smp_cores),
            "-boot", f"order={config.boot_order}",
            "-boot", f"menu={config.boot_menu}",
            "-serial", "stdio",                 # Serial output for better debugging
            "-monitor", "none",                 # Disable monitor for cleaner output
            "-nographic"                        # Text mode for better compatibility
        ]
        print("\n[AEGIS] Launching Project Obsoletion in auto-boot mode...")
        print("[AEGIS] The AI LLM should start automatically via startup.nsh...")
    else:
        # Traditional disk image approach
        qemu_command = [
            "qemu-system-x86_64",
            "-bios", config.bios_path,
            "-drive", f"file={config.disk_image},format=raw,index=0,media=disk",
            "-m", str(config.memory_mb),
            "-smp", str(config.smp_cores),
            "-boot", f"order={config.boot_order}",
            "-boot", f"menu={config.boot_menu}",
            # Adding some sensible defaults for a better experience
            "-vga", "virtio",                   # Better graphics performance
            "-display", "gtk,gl=on",            # Use GTK with OpenGL acceleration
            "-usb",                             # Enable USB support
            "-device", "usb-tablet",            # For accurate mouse pointer tracking
            "-net", "nic",                      # Add a network card
            "-net", "user,hostfwd=tcp::2222-:22" # User-mode networking with SSH port forward
        ]
        print("\n[AEGIS] Launching QEMU with traditional disk image...")

    print("\n[AEGIS] The specified reality is stable. Initiating QEMU launch sequence...")
    print("[AEGIS] Project Obsoletion neural network will initialize upon boot...")
    print("[AEGIS] Press Ctrl+C to exit when ready...")
    print("")

    _aegis_execute(qemu_command)
    print("\n[AEGIS] QEMU process has terminated.")
    print("[AEGIS] Project Obsoletion session ended.")

if __name__ == "__main__":
    main()