#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
[TMOP] QEMU Aegis Launcher

This script provides a resilient, configurable, and self-correcting wrapper
for launching QEMU virtual machines. It embodies the Aegis Prime principles by
validating prerequisites and adaptively healing the environment if required
state (like a disk image) is missing.
"""

import os
import sys
import subprocess
import shutil

# --- [TMOP] Aegis Prime: Core Configuration & Constants ---
# Layer 0: The Genesis Forge - Defining the default reality.

class QEMUConfig:
    """A simple class to hold our QEMU configuration."""
    def __init__(self):
        # Default UEFI firmware path for Debian/Ubuntu/Kali based systems.
        self.bios_path = "/usr/share/ovmf/OVMF.fd"
        # The disk image for the VM.
        self.disk_image = "boot_disk.img"
        # Default memory in MB.
        self.memory_mb = 2048
        # Default number of CPU cores.
        self.smp_cores = 2
        # Default boot order.
        self.boot_order = "c" # 'c' for disk, 'd' for cdrom
        # Show the boot menu on startup.
        self.boot_menu = "on"

def _aegis_execute(command: list, capture_output=False):
    """
    A universal guardian for executing external commands.
    Ensures commands are logged and handles errors gracefully.
    """
    print(f"[AEGIS] Executing: {' '.join(command)}")
    try:
        if capture_output:
            result = subprocess.run(command, check=True, text=True, capture_output=True)
            if result.stdout: print(f"[AEGIS] STDOUT: {result.stdout.strip()}")
            if result.stderr: print(f"[AEGIS] STDERR: {result.stderr.strip()}")
            return result
        else:
            subprocess.run(command, check=True)
            return True
    except FileNotFoundError:
        print(f"[AEGIS FAULT] Command not found: '{command[0]}'. Is the required program installed and in PATH?", file=sys.stderr)
        return None
    except subprocess.CalledProcessError as e:
        print(f"[AEGIS FAULT] Command failed with exit code {e.returncode}.", file=sys.stderr)
        if e.stdout: print(f"--- STDOUT ---\n{e.stdout}", file=sys.stderr)
        if e.stderr: print(f"--- STDERR ---\n{e.stderr}", file=sys.stderr)
        return None
    except Exception as e:
        print(f"[AEGIS FAULT] An unexpected anomaly occurred: {e}", file=sys.stderr)
        return None

# --- [TMOP] Aegis Prime: Layer 1 & 3 - Static Guardian & Adaptive Healer ---

def validate_and_heal_environment(config: QEMUConfig):
    """
    Ensures all prerequisite files exist. If the disk image is missing,
    it enters an 'adaptive healing' phase to create it.
    """
    print("\n[AEGIS] [Static Guardian]: Validating execution reality...")

    # 1. Validate BIOS/Firmware
    if not os.path.exists(config.bios_path):
        print(f"[AEGIS FAULT] BIOS file not found at '{config.bios_path}'.", file=sys.stderr)
        print("Please ensure the 'ovmf' package is installed ('sudo apt install ovmf').", file=sys.stderr)
        return False
    print(f"  [+] BIOS found: {config.bios_path}")

    # 2. Validate QEMU executable
    if not shutil.which("qemu-system-x86_64"):
        print("[AEGIS FAULT] 'qemu-system-x86_64' not found.", file=sys.stderr)
        print("Please ensure QEMU is installed ('sudo apt install qemu-system-x86').", file=sys.stderr)
        return False
    print("  [+] QEMU binary found.")

    # 3. Validate Disk Image (and heal if necessary)
    if not os.path.exists(config.disk_image):
        print(f"  [-] Disk image '{config.disk_image}' not found.")
        print("[AEGIS] [Adaptive Healer]: Entering healing routine to create disk image.")
        
        if not shutil.which("qemu-img"):
            print("[AEGIS FAULT] 'qemu-img' tool not found. Cannot create disk image.", file=sys.stderr)
            print("Please ensure QEMU is installed correctly.", file=sys.stderr)
            return False

        while True:
            try:
                size_gb_str = input("Enter desired disk size in GB [default: 20]: ")
                if not size_gb_str:
                    size_gb = 20
                else:
                    size_gb = int(size_gb_str)
                if size_gb <= 0:
                    raise ValueError("Size must be positive.")
                break
            except ValueError:
                print("Invalid input. Please enter a positive number.")
        
        create_command = ["qemu-img", "create", "-f", "raw", config.disk_image, f"{size_gb}G"]
        if _aegis_execute(create_command):
            print(f"  [+] Healing complete. New {size_gb}GB disk image '{config.disk_image}' created.")
        else:
            print("[AEGIS FAULT] Healing failed. Could not create disk image.", file=sys.stderr)
            return False
    else:
        print(f"  [+] Disk image found: {config.disk_image}")

    print("[AEGIS] [Static Guardian]: Reality is valid and stable.")
    return True

# --- [TMOP] Aegis Prime: Layer 6 - Ethereal User Interface ---

def show_menu(config: QEMUConfig):
    """Displays the configuration menu and allows user to modify it."""
    while True:
        print("\n" + "="*10 + " QEMU Aegis Launcher Menu " + "="*10)
        print(f" 1. Disk Image:     {config.disk_image}")
        print(f" 2. Memory (RAM):   {config.memory_mb} MB")
        print(f" 3. CPU Cores:      {config.smp_cores}")
        print(f" 4. BIOS Path:      {config.bios_path}")
        print("\n L. Launch QEMU with current settings")
        print(" Q. Quit")
        print("="*44)
        
        choice = input("Enter your choice to modify or launch: ").strip().lower()

        if choice == '1':
            new_val = input(f"Enter new disk image path [{config.disk_image}]: ")
            if new_val: config.disk_image = new_val
        elif choice == '2':
            try:
                new_val = input(f"Enter new memory in MB [{config.memory_mb}]: ")
                if new_val: config.memory_mb = int(new_val)
            except ValueError: print("Invalid number.")
        elif choice == '3':
            try:
                new_val = input(f"Enter new CPU core count [{config.smp_cores}]: ")
                if new_val: config.smp_cores = int(new_val)
            except ValueError: print("Invalid number.")
        elif choice == '4':
            new_val = input(f"Enter new BIOS path [{config.bios_path}]: ")
            if new_val: config.bios_path = new_val
        elif choice == 'l':
            return True
        elif choice == 'q':
            return False
        else:
            print("Invalid choice. Please try again.")

def main():
    """Main execution flow."""
    config = QEMUConfig()

    if not show_menu(config):
        print("[AEGIS] Launch aborted by operator.")
        sys.exit(0)

    if not validate_and_heal_environment(config):
        print("\n[AEGIS FAULT] Environment validation failed. Aborting launch.", file=sys.stderr)
        sys.exit(1)

    # Construct the final QEMU command
    qemu_command = [
        "qemu-system-x86_64",
        "-bios", config.bios_path,
        "-drive", f"file={config.disk_image},format=raw,index=0,media=disk",
        "-m", str(config.memory_mb),
        "-smp", str(config.smp_cores),
        "-boot", f"order={config.boot_order}",
        "-boot", f"menu={config.boot_menu}",
        # Adding some sensible defaults for a better experience
        "-vga", "virtio",                   # Better graphics performance
        "-display", "gtk,gl=on",            # Use GTK with OpenGL acceleration
        "-usb",                             # Enable USB support
        "-device", "usb-tablet",            # For accurate mouse pointer tracking
        "-net", "nic",                      # Add a network card
        "-net", "user,hostfwd=tcp::2222-:22" # User-mode networking with SSH port forward
    ]

    print("\n[AEGIS] The specified reality is stable. Initiating QEMU launch sequence...")
    _aegis_execute(qemu_command)
    print("\n[AEGIS] QEMU process has terminated.")

if __name__ == "__main__":
    main()