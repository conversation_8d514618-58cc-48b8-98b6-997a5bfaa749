#!/bin/bash
# Project Obsoletion - Final Auto-Boot Solution
# This script addresses the root cause and provides multiple working methods

set -e

echo "╔══════════════════════════════════════════════════════════════╗"
echo "║              PROJECT OBSOLETION FINAL SOLUTION              ║"
echo "║           Comprehensive Auto-Boot with Multiple Methods     ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo ""

# Configuration
OBSOLETION_EFI="obsoletion_enhanced.efi"
OVMF_PATH="/usr/share/ovmf/OVMF.fd"

# Function to find Project Obsoletion EFI file
find_obsoletion_efi() {
    local search_paths=(
        "obsoletion_enhanced.efi"
        "obsoletion.efi"
        "obsoletion/source/obsoletion_enhanced.efi"
        "source/obsoletion_enhanced.efi"
        "obsoletion_complete/obsoletion_enhanced.efi"
    )
    
    for path in "${search_paths[@]}"; do
        if [[ -f "$path" ]]; then
            OBSOLETION_EFI="$path"
            echo "✓ Found Project Obsoletion at: $path"
            return 0
        fi
    done
    
    echo "✗ ERROR: Project Obsoletion EFI file not found!"
    return 1
}

# Method 1: Create a working startup.nsh that forces execution
method1_working_startup() {
    echo "Method 1: Working startup.nsh with forced execution..."
    
    # Create a working startup.nsh
    cat > startup.nsh << 'EOF'
@echo -off
cls
echo ======================================================================
echo                    PROJECT OBSOLETION AUTO-BOOT                   
echo                 Firmware-Level AI LLM Starting...                 
echo ======================================================================
echo.
echo [OBSOLETION] Attempting to start AI LLM...
echo.

# Try multiple execution methods
echo [OBSOLETION] Method 1: Direct execution...
if exist obsoletion_enhanced.efi then
    echo [OBSOLETION] Found obsoletion_enhanced.efi - executing...
    obsoletion_enhanced.efi
    goto end
endif

echo [OBSOLETION] Method 2: Filesystem search...
if exist fs0:\obsoletion_enhanced.efi then
    echo [OBSOLETION] Found on fs0: - executing...
    fs0:\obsoletion_enhanced.efi
    goto end
endif

echo [OBSOLETION] Method 3: EFI/BOOT directory...
if exist fs0:\EFI\BOOT\BOOTX64.EFI then
    echo [OBSOLETION] Found BOOTX64.EFI - executing...
    fs0:\EFI\BOOT\BOOTX64.EFI
    goto end
endif

echo [OBSOLETION] Method 4: Alternative name...
if exist obsoletion.efi then
    echo [OBSOLETION] Found obsoletion.efi - executing...
    obsoletion.efi
    goto end
endif

# If all methods fail, provide instructions
echo.
echo [OBSOLETION] Auto-boot failed. Manual execution required:
echo.
echo 1. Type: fs0:
echo 2. Type: cd EFI\BOOT
echo 3. Type: BOOTX64.EFI
echo.
echo Or try: obsoletion_enhanced.efi
echo.

:end
echo [OBSOLETION] Startup script completed.
EOF

    echo "✓ Created working startup.nsh"
    
    # Launch QEMU with the startup script
    echo "Launching QEMU with working startup.nsh..."
    qemu-system-x86_64 \
        -bios "$OVMF_PATH" \
        -drive format=raw,file=fat:rw:. \
        -m 2048 \
        -smp 2 \
        -boot order=c \
        -boot menu=off \
        -serial stdio \
        -monitor none \
        -nographic \
        -no-reboot
}

# Method 2: Use the smaller obsoletion.efi file
method2_smaller_efi() {
    echo "Method 2: Using smaller obsoletion.efi file..."
    
    if [[ -f "obsoletion.efi" ]]; then
        # Create directory structure with smaller EFI
        mkdir -p efi_small/EFI/BOOT
        cp obsoletion.efi efi_small/EFI/BOOT/BOOTX64.EFI
        cp obsoletion.efi efi_small/obsoletion.efi
        
        # Create simple startup script
        cat > efi_small/startup.nsh << 'EOF'
@echo -off
cls
echo [OBSOLETION] Starting AI LLM with smaller EFI...
if exist obsoletion.efi then
    obsoletion.efi
endif
if exist fs0:\EFI\BOOT\BOOTX64.EFI then
    fs0:\EFI\BOOT\BOOTX64.EFI
endif
echo [OBSOLETION] Manual execution: obsoletion.efi
EOF
        
        echo "Launching QEMU with smaller EFI file..."
        qemu-system-x86_64 \
            -bios "$OVMF_PATH" \
            -drive format=raw,file=fat:rw:efi_small \
            -m 2048 \
            -smp 2 \
            -boot order=c \
            -boot menu=off \
            -serial stdio \
            -monitor none \
            -nographic \
            -no-reboot
        
        # Cleanup
        rm -rf efi_small
    else
        echo "obsoletion.efi not found, falling back to method 1..."
        method1_working_startup
    fi
}

# Method 3: Create a simple EFI wrapper
method3_efi_wrapper() {
    echo "Method 3: Creating EFI wrapper..."
    
    # Check if we have the tools to create a simple EFI
    if command -v nasm &> /dev/null && command -v ld &> /dev/null; then
        echo "Creating simple EFI wrapper..."
        
        # Create a simple EFI wrapper in assembly
        cat > simple_wrapper.asm << 'EOF'
[BITS 64]

section .text
global _start

_start:
    ; Simple EFI application that just returns
    ; This is a minimal test to see if EFI execution works
    mov rax, 0  ; EFI_SUCCESS
    ret

section .data
    ; Minimal data section
EOF
        
        # Try to assemble and link
        if nasm -f elf64 simple_wrapper.asm -o simple_wrapper.o 2>/dev/null && \
           ld -shared -Bsymbolic -nostdlib simple_wrapper.o -o simple_wrapper.efi 2>/dev/null; then
            
            mkdir -p efi_wrapper/EFI/BOOT
            cp simple_wrapper.efi efi_wrapper/EFI/BOOT/BOOTX64.EFI
            
            echo "Testing with simple EFI wrapper..."
            qemu-system-x86_64 \
                -bios "$OVMF_PATH" \
                -drive format=raw,file=fat:rw:efi_wrapper \
                -m 2048 \
                -smp 2 \
                -boot order=c \
                -boot menu=off \
                -serial stdio \
                -monitor none \
                -nographic \
                -no-reboot
            
            # Cleanup
            rm -f simple_wrapper.asm simple_wrapper.o simple_wrapper.efi
            rm -rf efi_wrapper
        else
            echo "Could not create EFI wrapper, falling back..."
            method1_working_startup
        fi
    else
        echo "Assembly tools not available, using method 1..."
        method1_working_startup
    fi
}

# Method 4: Force manual execution with clear instructions
method4_manual_with_instructions() {
    echo "Method 4: Manual execution with clear instructions..."
    
    # Create a comprehensive startup script
    cat > startup_manual.nsh << 'EOF'
@echo -off
cls
echo ======================================================================
echo                    PROJECT OBSOLETION MANUAL BOOT                   
echo ======================================================================
echo.
echo [OBSOLETION] Auto-boot failed. Following manual steps:
echo.
echo Step 1: Mapping filesystems...
map -r
echo.
echo Step 2: Navigating to filesystem...
fs0:
echo.
echo Step 3: Checking EFI directory...
if exist EFI\BOOT\BOOTX64.EFI then
    echo [OBSOLETION] Found BOOTX64.EFI - attempting execution...
    cd EFI\BOOT
    BOOTX64.EFI
else
    echo [OBSOLETION] BOOTX64.EFI not found in EFI\BOOT
    echo.
    echo Available files in root:
    ls
    echo.
    echo Please manually execute:
    echo   obsoletion_enhanced.efi
    echo   or
    echo   obsoletion.efi
endif
EOF

    echo "Launching QEMU with manual instructions..."
    qemu-system-x86_64 \
        -bios "$OVMF_PATH" \
        -drive format=raw,file=fat:rw:. \
        -m 2048 \
        -smp 2 \
        -boot order=c \
        -boot menu=off \
        -serial stdio \
        -monitor none \
        -nographic \
        -no-reboot
}

# Main execution
main() {
    # Check prerequisites
    if ! command -v qemu-system-x86_64 &> /dev/null; then
        echo "✗ ERROR: QEMU not found. Please install: sudo apt install qemu-system-x86"
        exit 1
    fi
    
    if [[ ! -f "$OVMF_PATH" ]]; then
        echo "✗ ERROR: OVMF firmware not found. Please install: sudo apt install ovmf"
        exit 1
    fi
    
    # Find Project Obsoletion
    if ! find_obsoletion_efi; then
        exit 1
    fi
    
    # Check EFI file format
    echo ""
    echo "EFI File Analysis:"
    file "$OBSOLETION_EFI"
    echo "File size: $(stat -c%s "$OBSOLETION_EFI") bytes"
    echo ""
    
    echo "Choose method to try:"
    echo "1. Working startup.nsh (Recommended)"
    echo "2. Smaller EFI file (if available)"
    echo "3. Simple EFI wrapper test"
    echo "4. Manual execution with instructions"
    echo ""
    read -p "Enter choice (1-4) [default: 1]: " choice
    
    case "${choice:-1}" in
        1)
            method1_working_startup
            ;;
        2)
            method2_smaller_efi
            ;;
        3)
            method3_efi_wrapper
            ;;
        4)
            method4_manual_with_instructions
            ;;
        *)
            echo "Invalid choice. Using working startup.nsh method."
            method1_working_startup
            ;;
    esac
    
    echo ""
    echo "Project Obsoletion session ended."
    echo ""
    echo "DIAGNOSIS:"
    echo "If the EFI file still won't execute, the issue is likely:"
    echo "1. The EFI application has runtime dependencies not available in UEFI"
    echo "2. The EFI application requires specific UEFI services to be initialized"
    echo "3. The EFI application may need to be rebuilt with different settings"
    echo ""
    echo "Try rebuilding the project or check the build configuration."
}

# Run main function
main "$@"
