#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Project Obsoletion - Enhanced GUI Launcher
Provides multiple launch options for the AI LLM system with automatic boot capabilities.
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading

class ObsoletionLauncher:
    def __init__(self, root):
        self.root = root
        self.root.title("Project Obsoletion - AI LLM Launcher")
        self.root.geometry("800x600")
        self.root.configure(bg='#1a1a1a')
        
        # Configuration
        self.config = {
            'memory': 2048,
            'cpu_cores': 2,
            'auto_boot': True,
            'efi_file': 'obsoletion_enhanced.efi',
            'ovmf_path': '/usr/share/ovmf/OVMF.fd'
        }
        
        self.setup_ui()
        self.find_efi_file()
    
    def setup_ui(self):
        # Title
        title_frame = tk.Frame(self.root, bg='#1a1a1a')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(title_frame, 
                              text="PROJECT OBSOLETION", 
                              font=('Arial', 24, 'bold'),
                              fg='#00ff00', bg='#1a1a1a')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame,
                                 text="Firmware-Level AI LLM System",
                                 font=('Arial', 12),
                                 fg='#ffffff', bg='#1a1a1a')
        subtitle_label.pack()
        
        # Status frame
        status_frame = tk.Frame(self.root, bg='#2a2a2a', relief='raised', bd=2)
        status_frame.pack(fill='x', padx=20, pady=10)
        
        self.status_label = tk.Label(status_frame,
                                    text="System Status: Ready",
                                    font=('Arial', 10),
                                    fg='#00ff00', bg='#2a2a2a')
        self.status_label.pack(pady=5)
        
        # Configuration frame
        config_frame = tk.LabelFrame(self.root, text="Configuration", 
                                   font=('Arial', 12, 'bold'),
                                   fg='#ffffff', bg='#1a1a1a')
        config_frame.pack(fill='x', padx=20, pady=10)
        
        # Memory setting
        memory_frame = tk.Frame(config_frame, bg='#1a1a1a')
        memory_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(memory_frame, text="Memory (MB):", 
                fg='#ffffff', bg='#1a1a1a').pack(side='left')
        
        self.memory_var = tk.StringVar(value=str(self.config['memory']))
        memory_entry = tk.Entry(memory_frame, textvariable=self.memory_var, width=10)
        memory_entry.pack(side='right')
        
        # CPU cores setting
        cpu_frame = tk.Frame(config_frame, bg='#1a1a1a')
        cpu_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(cpu_frame, text="CPU Cores:", 
                fg='#ffffff', bg='#1a1a1a').pack(side='left')
        
        self.cpu_var = tk.StringVar(value=str(self.config['cpu_cores']))
        cpu_entry = tk.Entry(cpu_frame, textvariable=self.cpu_var, width=10)
        cpu_entry.pack(side='right')
        
        # Auto-boot setting
        auto_frame = tk.Frame(config_frame, bg='#1a1a1a')
        auto_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(auto_frame, text="Auto-boot AI LLM:", 
                fg='#ffffff', bg='#1a1a1a').pack(side='left')
        
        self.auto_boot_var = tk.BooleanVar(value=self.config['auto_boot'])
        auto_check = tk.Checkbutton(auto_frame, variable=self.auto_boot_var,
                                   bg='#1a1a1a', fg='#ffffff',
                                   selectcolor='#2a2a2a')
        auto_check.pack(side='right')
        
        # EFI file setting
        efi_frame = tk.Frame(config_frame, bg='#1a1a1a')
        efi_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(efi_frame, text="EFI File:", 
                fg='#ffffff', bg='#1a1a1a').pack(side='left')
        
        self.efi_var = tk.StringVar(value=self.config['efi_file'])
        efi_entry = tk.Entry(efi_frame, textvariable=self.efi_var, width=30)
        efi_entry.pack(side='right', padx=(0, 5))
        
        browse_btn = tk.Button(efi_frame, text="Browse", 
                              command=self.browse_efi_file,
                              bg='#3a3a3a', fg='#ffffff')
        browse_btn.pack(side='right')
        
        # Launch buttons frame
        buttons_frame = tk.Frame(self.root, bg='#1a1a1a')
        buttons_frame.pack(fill='x', padx=20, pady=20)
        
        # Auto-boot button (recommended)
        auto_btn = tk.Button(buttons_frame, 
                           text="🚀 AUTO-LAUNCH AI LLM\n(Recommended)",
                           font=('Arial', 14, 'bold'),
                           bg='#00aa00', fg='#ffffff',
                           height=3, width=25,
                           command=self.auto_launch)
        auto_btn.pack(side='left', padx=10)
        
        # Manual launch button
        manual_btn = tk.Button(buttons_frame,
                             text="⚙️ MANUAL LAUNCH\n(Advanced)",
                             font=('Arial', 12),
                             bg='#aa6600', fg='#ffffff',
                             height=3, width=25,
                             command=self.manual_launch)
        manual_btn.pack(side='left', padx=10)
        
        # GUI launcher button
        gui_btn = tk.Button(buttons_frame,
                          text="🖥️ GUI LAUNCHER\n(Interactive)",
                          font=('Arial', 12),
                          bg='#0066aa', fg='#ffffff',
                          height=3, width=25,
                          command=self.gui_launch)
        gui_btn.pack(side='left', padx=10)
        
        # Output frame
        output_frame = tk.LabelFrame(self.root, text="System Output",
                                   font=('Arial', 12, 'bold'),
                                   fg='#ffffff', bg='#1a1a1a')
        output_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        self.output_text = tk.Text(output_frame, 
                                  bg='#000000', fg='#00ff00',
                                  font=('Courier', 10),
                                  wrap='word')
        
        scrollbar = tk.Scrollbar(output_frame, command=self.output_text.yview)
        self.output_text.configure(yscrollcommand=scrollbar.set)
        
        self.output_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Initial message
        self.log_output("Project Obsoletion GUI Launcher initialized.")
        self.log_output("Ready to launch firmware-level AI LLM system.")
    
    def find_efi_file(self):
        """Find the Project Obsoletion EFI file."""
        search_paths = [
            'obsoletion_enhanced.efi',
            'obsoletion.efi',
            'obsoletion/source/obsoletion_enhanced.efi',
            'source/obsoletion_enhanced.efi',
            'obsoletion_complete/obsoletion_enhanced.efi'
        ]
        
        for path in search_paths:
            if os.path.exists(path):
                self.config['efi_file'] = path
                self.efi_var.set(path)
                self.log_output(f"Found Project Obsoletion EFI: {path}")
                self.status_label.config(text="System Status: EFI File Located", fg='#00ff00')
                return
        
        self.log_output("WARNING: Project Obsoletion EFI file not found!")
        self.status_label.config(text="System Status: EFI File Missing", fg='#ff6600')
    
    def browse_efi_file(self):
        """Browse for EFI file."""
        filename = filedialog.askopenfilename(
            title="Select Project Obsoletion EFI File",
            filetypes=[("EFI Files", "*.efi"), ("All Files", "*.*")]
        )
        if filename:
            self.efi_var.set(filename)
            self.config['efi_file'] = filename
            self.log_output(f"Selected EFI file: {filename}")
    
    def log_output(self, message):
        """Add message to output text widget."""
        self.output_text.insert(tk.END, f"[{self.get_timestamp()}] {message}\n")
        self.output_text.see(tk.END)
        self.root.update_idletasks()
    
    def get_timestamp(self):
        """Get current timestamp."""
        import datetime
        return datetime.datetime.now().strftime("%H:%M:%S")
    
    def update_config(self):
        """Update configuration from GUI."""
        try:
            self.config['memory'] = int(self.memory_var.get())
            self.config['cpu_cores'] = int(self.cpu_var.get())
            self.config['auto_boot'] = self.auto_boot_var.get()
            self.config['efi_file'] = self.efi_var.get()
            return True
        except ValueError as e:
            messagebox.showerror("Configuration Error", f"Invalid configuration: {e}")
            return False
    
    def auto_launch(self):
        """Launch with automatic boot script."""
        if not self.update_config():
            return
        
        self.log_output("Starting Project Obsoletion with automatic boot...")
        self.status_label.config(text="System Status: Launching AI LLM...", fg='#ffff00')
        
        def run_auto_launch():
            try:
                # Use the auto-boot script
                result = subprocess.run(['./auto_boot_obsoletion.sh'], 
                                      capture_output=True, text=True)
                
                if result.stdout:
                    self.log_output("STDOUT: " + result.stdout)
                if result.stderr:
                    self.log_output("STDERR: " + result.stderr)
                
                self.status_label.config(text="System Status: Launch Complete", fg='#00ff00')
                
            except Exception as e:
                self.log_output(f"Launch error: {e}")
                self.status_label.config(text="System Status: Launch Failed", fg='#ff0000')
        
        # Run in separate thread to prevent GUI freezing
        thread = threading.Thread(target=run_auto_launch)
        thread.daemon = True
        thread.start()
    
    def manual_launch(self):
        """Launch with manual QEMU command."""
        if not self.update_config():
            return
        
        self.log_output("Starting Project Obsoletion with manual configuration...")
        self.status_label.config(text="System Status: Manual Launch...", fg='#ffff00')
        
        # Build QEMU command
        cmd = [
            'qemu-system-x86_64',
            '-bios', self.config['ovmf_path'],
            '-drive', 'format=raw,file=fat:rw:.',
            '-m', str(self.config['memory']),
            '-smp', str(self.config['cpu_cores']),
            '-nographic',
            '-serial', 'stdio',
            '-monitor', 'none'
        ]
        
        if self.config['auto_boot']:
            cmd.extend(['-boot', 'menu=off'])
        else:
            cmd.extend(['-boot', 'menu=on'])
        
        def run_manual_launch():
            try:
                self.log_output(f"Executing: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.stdout:
                    self.log_output("STDOUT: " + result.stdout)
                if result.stderr:
                    self.log_output("STDERR: " + result.stderr)
                
                self.status_label.config(text="System Status: Launch Complete", fg='#00ff00')
                
            except Exception as e:
                self.log_output(f"Launch error: {e}")
                self.status_label.config(text="System Status: Launch Failed", fg='#ff0000')
        
        thread = threading.Thread(target=run_manual_launch)
        thread.daemon = True
        thread.start()
    
    def gui_launch(self):
        """Launch the original GUI launcher."""
        self.log_output("Starting interactive QEMU GUI launcher...")
        
        def run_gui_launch():
            try:
                subprocess.run([sys.executable, 'qemu.py'])
            except Exception as e:
                self.log_output(f"GUI launch error: {e}")
        
        thread = threading.Thread(target=run_gui_launch)
        thread.daemon = True
        thread.start()

def main():
    root = tk.Tk()
    app = ObsoletionLauncher(root)
    root.mainloop()

if __name__ == "__main__":
    main()
