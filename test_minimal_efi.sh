#!/bin/bash
# Test the minimal EFI application

echo "Testing minimal EFI application..."

mkdir -p test_minimal/EFI/BOOT
cp minimal_test.efi test_minimal/EFI/BOOT/BOOTX64.EFI
cp minimal_test.efi test_minimal/minimal_test.efi

cat > test_minimal/startup.nsh << 'EOF'
cls
echo Testing minimal EFI application...
if exist minimal_test.efi then
    echo Executing minimal_test.efi...
    minimal_test.efi
    echo Return code: %lasterror%
else
    echo minimal_test.efi not found
endif
echo Test completed.
EOF

echo "Starting QEMU with minimal EFI test..."

qemu-system-x86_64 \
    -bios /usr/share/ovmf/OVMF.fd \
    -drive format=raw,file=fat:rw:test_minimal \
    -m 512 \
    -smp 1 \
    -boot order=c \
    -boot menu=off \
    -serial stdio \
    -monitor none \
    -nographic \
    -no-reboot

rm -rf test_minimal
