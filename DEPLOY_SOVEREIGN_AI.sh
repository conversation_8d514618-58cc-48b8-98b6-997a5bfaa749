#!/bin/bash
# SOVEREIGN-AI Deployment Script
# The Ultimate AI Empire: REIGN + Agent Zero + Code Companion + SillyTavern

echo "🔥🔥🔥 SOVEREIGN-AI DEPLOYMENT 🔥🔥🔥"
echo "========================================"
echo "🧠 REIGN Neural Core (Project Obsoletion)"
echo "🤖 Agent Zero Multi-Agent Framework" 
echo "💻 Code Companion AI Coding Assistant"
echo "💬 SillyTavern Advanced Chat Interface"
echo "========================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[ℹ]${NC} $1"
}

print_step() {
    echo -e "${PURPLE}[🚀]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "SOVEREIGN_AI_MASTER_INTEGRATION.py" ]; then
    print_error "SOVEREIGN-AI files not found. Please run from the project directory."
    exit 1
fi

print_step "Checking SOVEREIGN-AI components..."

# Check REIGN (Project Obsoletion)
if [ -f "obsoletion_enhanced.efi" ] || [ -f "obsoletion.efi" ]; then
    print_status "REIGN neural core found"
    REIGN_AVAILABLE=true
else
    print_warning "REIGN neural core not found - will build if needed"
    REIGN_AVAILABLE=false
fi

# Check Agent Zero
if [ -d "agent-zero" ] && [ -f "agent-zero/agent.py" ]; then
    print_status "Agent Zero framework found"
    AGENT_ZERO_AVAILABLE=true
else
    print_error "Agent Zero not found"
    AGENT_ZERO_AVAILABLE=false
fi

# Check Code Companion
if [ -d "code-companion" ] && [ -f "code-companion/README.md" ]; then
    print_status "Code Companion found"
    CODE_COMPANION_AVAILABLE=true
else
    print_error "Code Companion not found"
    CODE_COMPANION_AVAILABLE=false
fi

# Check SillyTavern
if [ -d "silly-tavern" ] && [ -f "silly-tavern/server.js" ]; then
    print_status "SillyTavern found"
    SILLY_TAVERN_AVAILABLE=true
else
    print_error "SillyTavern not found"
    SILLY_TAVERN_AVAILABLE=false
fi

echo ""
print_step "Checking system dependencies..."

# Check Python
if command -v python3 &> /dev/null; then
    print_status "Python 3 found"
else
    print_error "Python 3 not found. Please install Python 3."
    exit 1
fi

# Check Node.js for SillyTavern
if command -v node &> /dev/null; then
    print_status "Node.js found"
else
    print_warning "Node.js not found. SillyTavern will not work without it."
fi

# Check QEMU for REIGN
if command -v qemu-system-x86_64 &> /dev/null; then
    print_status "QEMU found"
else
    print_warning "QEMU not found. REIGN neural core requires QEMU."
fi

echo ""
print_step "SOVEREIGN-AI Deployment Options:"
echo ""
echo "1. 🚀 Full SOVEREIGN-AI Deployment (All Components)"
echo "2. 🧠 REIGN Neural Core Only"
echo "3. 🤖 Agent Zero + REIGN Integration"
echo "4. 💬 SillyTavern + REIGN Integration"
echo "5. 💻 Code Companion + REIGN Integration"
echo "6. 🔧 Remove Docker Dependencies (Agent Zero)"
echo "7. 🎮 Interactive SOVEREIGN-AI Control Panel"
echo "8. ❌ Exit"
echo ""

read -p "Choose deployment option (1-8): " choice

case $choice in
    1)
        print_step "Deploying Full SOVEREIGN-AI Empire..."
        echo ""
        
        # Remove Docker dependencies first
        print_info "Removing Docker dependencies from Agent Zero..."
        python3 remove_docker_dependencies.py
        
        echo ""
        print_info "Setting up REIGN integration bridge..."
        python3 reign_integration_bridge.py &
        BRIDGE_PID=$!
        
        sleep 3
        
        echo ""
        print_info "Starting SOVEREIGN-AI Master Control..."
        python3 SOVEREIGN_AI_MASTER_INTEGRATION.py
        
        # Cleanup
        kill $BRIDGE_PID 2>/dev/null
        ;;
        
    2)
        print_step "Starting REIGN Neural Core..."
        if [ "$REIGN_AVAILABLE" = true ]; then
            python3 obsoletion_monitor.py
        else
            print_error "REIGN neural core not available. Please build Project Obsoletion first."
        fi
        ;;
        
    3)
        print_step "Starting Agent Zero with REIGN integration..."
        if [ "$AGENT_ZERO_AVAILABLE" = true ]; then
            # Remove Docker dependencies
            python3 remove_docker_dependencies.py
            
            # Start REIGN bridge
            python3 reign_integration_bridge.py &
            BRIDGE_PID=$!
            
            sleep 3
            
            # Start Agent Zero
            cd agent-zero
            python3 launch_native.py
            cd ..
            
            # Cleanup
            kill $BRIDGE_PID 2>/dev/null
        else
            print_error "Agent Zero not available."
        fi
        ;;
        
    4)
        print_step "Starting SillyTavern with REIGN integration..."
        if [ "$SILLY_TAVERN_AVAILABLE" = true ]; then
            # Start REIGN bridge
            python3 reign_integration_bridge.py &
            BRIDGE_PID=$!
            
            sleep 3
            
            # Start SillyTavern
            cd silly-tavern
            npm install
            node server.js
            cd ..
            
            # Cleanup
            kill $BRIDGE_PID 2>/dev/null
        else
            print_error "SillyTavern not available."
        fi
        ;;
        
    5)
        print_step "Setting up Code Companion with REIGN integration..."
        if [ "$CODE_COMPANION_AVAILABLE" = true ]; then
            python3 reign_integration_bridge.py
            print_status "Code Companion REIGN integration ready!"
            print_info "Use the Lua interface in code-companion/lua/reign/interface.lua"
        else
            print_error "Code Companion not available."
        fi
        ;;
        
    6)
        print_step "Removing Docker dependencies from Agent Zero..."
        python3 remove_docker_dependencies.py
        ;;
        
    7)
        print_step "Starting Interactive SOVEREIGN-AI Control Panel..."
        python3 SOVEREIGN_AI_MASTER_INTEGRATION.py
        ;;
        
    8)
        print_info "Exiting SOVEREIGN-AI deployment."
        exit 0
        ;;
        
    *)
        print_error "Invalid option. Please choose 1-8."
        exit 1
        ;;
esac

echo ""
print_step "SOVEREIGN-AI Deployment Complete!"
echo ""
print_status "🎉 Welcome to the future of AI!"
print_info "Your AI empire is now operational:"
echo ""
echo -e "${CYAN}🧠 REIGN${NC} - Firmware-level neural network (0.19ms inference)"
echo -e "${CYAN}🤖 Agent Zero${NC} - Multi-agent automation framework"  
echo -e "${CYAN}💻 Code Companion${NC} - AI coding assistant"
echo -e "${CYAN}💬 SillyTavern${NC} - Advanced chat interface"
echo ""
print_info "All systems integrated and API-independent!"
print_info "SOVEREIGN-AI: Complete AI autonomy achieved! 🚀"
