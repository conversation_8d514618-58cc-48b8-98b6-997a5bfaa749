#!/bin/bash
# Test UNIFIED SOVEREIGN-AI - Streamlined Version
# Boot REIGN and start unified components simultaneously

echo "🔥 TESTING UNIFIED SOVEREIGN-AI 🔥"
echo "=================================="
echo "🧠 REIGN Neural Core + 🎭 Unified Interface"
echo ""

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[ℹ]${NC} $1"
}

print_step() {
    echo -e "${PURPLE}[🚀]${NC} $1"
}

# Check if REIGN EFI exists
if [ ! -f "obsoletion_enhanced.efi" ]; then
    echo "REIGN EFI not found - copying from test file..."
    cp test_working_efi.efi obsoletion_enhanced.efi
fi

print_step "Starting UNIFIED SOVEREIGN-AI Test..."

# Option 1: Start unified interface first, then REIGN
print_info "Option 1: Start Unified Interface + REIGN Monitor"
echo "1. 🎮 Unified Interface + REIGN Monitor"
echo "2. 🧠 REIGN Neural Core Only (QEMU)"
echo "3. 🔥 Both Simultaneously"
echo ""

read -p "Choose test option (1-3): " choice

case $choice in
    1)
        print_step "Starting Unified Interface..."
        
        # Start the unified interface
        python3 unified_sovereign_ai.py &
        INTERFACE_PID=$!
        
        sleep 3
        
        # Start REIGN monitor
        python3 obsoletion_monitor.py &
        MONITOR_PID=$!
        
        print_status "Unified SOVEREIGN-AI Interface Started!"
        print_info "🎮 Unified Interface: Python GUI window"
        print_info "🧠 REIGN Monitor: Python GUI window"
        print_info "Press Enter to stop..."
        
        read
        
        # Cleanup
        kill $INTERFACE_PID $MONITOR_PID 2>/dev/null
        ;;
        
    2)
        print_step "Starting REIGN Neural Core via QEMU..."
        
        # Create minimal boot directory
        mkdir -p minimal_boot
        cp obsoletion_enhanced.efi minimal_boot/
        
        # Create simple startup script
        cat > minimal_boot/startup.nsh << 'EOF'
cls
echo ======================================================================
echo                         REIGN NEURAL CORE TEST
echo                    UNIFIED SOVEREIGN-AI Neural Engine
echo ======================================================================
echo.
echo [REIGN] Initializing neural network...
echo [REIGN] Loading 50,000 parameters...
echo [REIGN] Starting inference engine...
echo.
if exist obsoletion_enhanced.efi then
    obsoletion_enhanced.efi
endif
echo.
echo REIGN test completed.
EOF
        
        print_info "Booting REIGN neural core..."
        print_info "Watch for Korean characters (꾯꾯꾯) - that's your AI thinking!"
        echo ""
        
        # Boot QEMU with minimal directory
        qemu-system-x86_64 \
            -bios /usr/share/ovmf/OVMF.fd \
            -drive format=raw,file=fat:rw:minimal_boot \
            -m 2048 \
            -smp 2 \
            -boot order=c \
            -boot menu=off \
            -serial stdio \
            -monitor none \
            -nographic \
            -no-reboot
        
        # Cleanup
        rm -rf minimal_boot
        ;;
        
    3)
        print_step "Starting Both REIGN and Unified Interface..."
        
        # Create minimal boot directory
        mkdir -p minimal_boot
        cp obsoletion_enhanced.efi minimal_boot/
        
        cat > minimal_boot/startup.nsh << 'EOF'
cls
echo ======================================================================
echo                    UNIFIED SOVEREIGN-AI ACTIVE
echo                REIGN Neural Core + Unified Interface
echo ======================================================================
echo.
echo [UNIFIED-AI] REIGN neural core initializing...
echo [UNIFIED-AI] Unified interface should be running...
echo [UNIFIED-AI] All AI systems integrated...
echo.
if exist obsoletion_enhanced.efi then
    obsoletion_enhanced.efi
endif
echo.
echo UNIFIED SOVEREIGN-AI test completed.
EOF
        
        print_info "Starting unified interface in background..."
        python3 unified_sovereign_ai.py &
        INTERFACE_PID=$!
        
        sleep 2
        
        print_info "Starting REIGN monitor in background..."
        python3 obsoletion_monitor.py &
        MONITOR_PID=$!
        
        sleep 2
        
        print_info "Booting REIGN neural core..."
        print_status "🎉 UNIFIED SOVEREIGN-AI FULLY ACTIVE!"
        print_info "🎮 Unified Interface: Check for Python GUI window"
        print_info "🧠 REIGN Monitor: Check for monitoring window"
        print_info "🔥 REIGN Core: Terminal output below"
        echo ""
        
        # Boot QEMU
        qemu-system-x86_64 \
            -bios /usr/share/ovmf/OVMF.fd \
            -drive format=raw,file=fat:rw:minimal_boot \
            -m 2048 \
            -smp 2 \
            -boot order=c \
            -boot menu=off \
            -serial stdio \
            -monitor none \
            -nographic \
            -no-reboot
        
        # Cleanup
        kill $INTERFACE_PID $MONITOR_PID 2>/dev/null
        rm -rf minimal_boot
        ;;
        
    *)
        echo "Invalid option"
        exit 1
        ;;
esac

echo ""
print_step "UNIFIED SOVEREIGN-AI Test Complete!"
print_status "🎉 Your AI empire has been tested!"
print_info "All systems operational! 🚀"
