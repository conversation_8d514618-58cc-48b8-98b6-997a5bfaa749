#!/usr/bin/env python3
"""
Docker Dependency Removal for SOVEREIGN-AI
Removes all Docker dependencies from Agent Zero and makes it run natively
with REIGN neural core integration
"""

import os
import re
import shutil
from pathlib import Path

class DockerRemover:
    def __init__(self):
        self.agent_zero_path = Path("agent-zero")
        self.backup_path = Path("agent-zero-backup")
        
    def backup_agent_zero(self):
        """Create backup of original Agent Zero."""
        if self.backup_path.exists():
            shutil.rmtree(self.backup_path)
        
        shutil.copytree(self.agent_zero_path, self.backup_path)
        print("✓ Agent Zero backed up to agent-zero-backup/")
    
    def remove_docker_files(self):
        """Remove Docker-related files."""
        docker_files = [
            "docker",
            "Dockerfile", 
            "docker-compose.yml",
            ".dockerignore"
        ]
        
        for file in docker_files:
            file_path = self.agent_zero_path / file
            if file_path.exists():
                if file_path.is_dir():
                    shutil.rmtree(file_path)
                else:
                    file_path.unlink()
                print(f"✓ Removed {file}")
    
    def patch_agent_py(self):
        """Patch main agent.py to remove Docker dependencies."""
        agent_file = self.agent_zero_path / "agent.py"
        
        with open(agent_file, 'r') as f:
            content = f.read()
        
        # Remove Docker imports and references
        content = re.sub(r'import docker.*\n', '', content)
        content = re.sub(r'from docker.*\n', '', content)
        content = re.sub(r'.*docker.*\n', '', content, flags=re.IGNORECASE)
        
        # Replace Docker execution with native execution
        docker_replacement = '''
# Native execution replacement for Docker
import subprocess
import tempfile
import os

class NativeExecutor:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
    
    def run_code(self, code, language="python"):
        """Run code natively instead of in Docker."""
        if language.lower() == "python":
            return self._run_python(code)
        elif language.lower() in ["bash", "shell"]:
            return self._run_bash(code)
        else:
            return f"Language {language} not supported in native mode"
    
    def _run_python(self, code):
        """Run Python code natively."""
        try:
            temp_file = os.path.join(self.temp_dir, "temp_script.py")
            with open(temp_file, 'w') as f:
                f.write(code)
            
            result = subprocess.run(['python3', temp_file], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                return result.stdout
            else:
                return f"Error: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            return "Error: Code execution timed out"
        except Exception as e:
            return f"Error: {str(e)}"
    
    def _run_bash(self, code):
        """Run bash code natively."""
        try:
            result = subprocess.run(['bash', '-c', code], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                return result.stdout
            else:
                return f"Error: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            return "Error: Command execution timed out"
        except Exception as e:
            return f"Error: {str(e)}"

# Global native executor
native_executor = NativeExecutor()
'''
        
        content = docker_replacement + content
        
        with open(agent_file, 'w') as f:
            f.write(content)
        
        print("✓ Agent.py patched for native execution")
    
    def patch_tools(self):
        """Patch tools to use native execution instead of Docker."""
        tools_path = self.agent_zero_path / "python" / "tools"
        
        if not tools_path.exists():
            print("⚠ Tools directory not found, skipping tool patches")
            return
        
        for tool_file in tools_path.glob("*.py"):
            with open(tool_file, 'r') as f:
                content = f.read()
            
            # Replace Docker calls with native execution
            if 'docker' in content.lower():
                content = re.sub(r'.*docker.*', '# Docker removed - using native execution', content, flags=re.IGNORECASE)
                
                # Add native execution import
                if 'import subprocess' not in content:
                    content = 'import subprocess\n' + content
                
                with open(tool_file, 'w') as f:
                    f.write(content)
                
                print(f"✓ Patched {tool_file.name}")
    
    def create_native_config(self):
        """Create configuration for native execution."""
        config_content = '''
# SOVEREIGN-AI Native Configuration
# Docker-free Agent Zero configuration with REIGN integration

import os

# Native execution settings
NATIVE_EXECUTION = True
DOCKER_ENABLED = False

# REIGN integration settings
REIGN_ENABLED = True
REIGN_EFI_PATH = "obsoletion_enhanced.efi"

# Code execution settings
CODE_EXEC_DOCKER_ENABLED = False
CODE_EXEC_NATIVE_ENABLED = True
CODE_EXEC_TIMEOUT = 30

# Security settings for native execution
ALLOWED_COMMANDS = [
    "python3", "python", "node", "npm", "pip", "pip3",
    "ls", "cat", "grep", "find", "curl", "wget"
]

BLOCKED_COMMANDS = [
    "rm -rf", "sudo", "su", "chmod 777", "dd", "mkfs"
]

# REIGN neural core settings
REIGN_INFERENCE_TIMEOUT = 5.0
REIGN_MAX_TOKENS = 2048
REIGN_TEMPERATURE = 0.7

# Agent Zero native settings
AGENT_MEMORY_PATH = "./agent-zero-data/memory"
AGENT_KNOWLEDGE_PATH = "./agent-zero-data/knowledge"
AGENT_WORK_DIR = "./agent-zero-data/work_dir"

print("✓ SOVEREIGN-AI Native Configuration Loaded")
print("🧠 REIGN Neural Core Integration Active")
print("🤖 Agent Zero Docker-Free Mode Enabled")
'''
        
        config_file = self.agent_zero_path / "native_config.py"
        with open(config_file, 'w') as f:
            f.write(config_content)
        
        print("✓ Native configuration created")
    
    def patch_requirements(self):
        """Update requirements.txt to remove Docker dependencies."""
        req_file = self.agent_zero_path / "requirements.txt"
        
        if not req_file.exists():
            print("⚠ requirements.txt not found")
            return
        
        with open(req_file, 'r') as f:
            requirements = f.readlines()
        
        # Remove Docker-related requirements
        filtered_requirements = []
        for req in requirements:
            if not any(docker_term in req.lower() for docker_term in ['docker', 'container']):
                filtered_requirements.append(req)
        
        # Add REIGN integration requirements
        additional_requirements = [
            "asyncio\n",
            "flask\n", 
            "websockets\n",
            "psutil\n"
        ]
        
        filtered_requirements.extend(additional_requirements)
        
        with open(req_file, 'w') as f:
            f.writelines(filtered_requirements)
        
        print("✓ Requirements.txt updated for native execution")
    
    def create_native_launcher(self):
        """Create native launcher script."""
        launcher_content = '''#!/usr/bin/env python3
"""
SOVEREIGN-AI Agent Zero Native Launcher
Docker-free Agent Zero with REIGN neural core integration
"""

import os
import sys
import subprocess
import time

def check_reign_core():
    """Check if REIGN neural core is available."""
    if os.path.exists("obsoletion_enhanced.efi") or os.path.exists("obsoletion.efi"):
        print("✓ REIGN neural core found")
        return True
    else:
        print("⚠ REIGN neural core not found - some features may be limited")
        return False

def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing SOVEREIGN-AI dependencies...")
    subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])

def start_agent_zero_native():
    """Start Agent Zero in native mode."""
    print("🚀 Starting SOVEREIGN-AI Agent Zero (Native Mode)")
    print("🧠 REIGN Neural Core Integration Active")
    print("=" * 50)
    
    # Set environment variables
    os.environ["SOVEREIGN_AI_MODE"] = "true"
    os.environ["DOCKER_ENABLED"] = "false"
    os.environ["NATIVE_EXECUTION"] = "true"
    
    # Start Agent Zero UI
    try:
        subprocess.run([sys.executable, "run_ui.py"])
    except KeyboardInterrupt:
        print("\\n⏹️ SOVEREIGN-AI Agent Zero stopped")

def main():
    print("🔥 SOVEREIGN-AI Agent Zero Native Launcher")
    print("Docker-Free • REIGN-Powered • Ultimate AI")
    print("=" * 50)
    
    # Check REIGN
    check_reign_core()
    
    # Install dependencies
    install_dependencies()
    
    # Start Agent Zero
    start_agent_zero_native()

if __name__ == "__main__":
    main()
'''
        
        launcher_file = self.agent_zero_path / "launch_native.py"
        with open(launcher_file, 'w') as f:
            f.write(launcher_content)
        
        os.chmod(launcher_file, 0o755)
        print("✓ Native launcher created")
    
    def remove_all_docker_dependencies(self):
        """Remove all Docker dependencies from Agent Zero."""
        print("🔥 Removing Docker dependencies from Agent Zero...")
        print("=" * 50)
        
        # Backup first
        self.backup_agent_zero()
        
        # Remove Docker files
        self.remove_docker_files()
        
        # Patch Python files
        self.patch_agent_py()
        self.patch_tools()
        
        # Create native configuration
        self.create_native_config()
        self.patch_requirements()
        self.create_native_launcher()
        
        print("=" * 50)
        print("✅ Docker removal complete!")
        print("🚀 Agent Zero is now Docker-free and ready for REIGN integration!")
        print("📁 Original files backed up to agent-zero-backup/")
        print("🎯 Use 'python3 agent-zero/launch_native.py' to start")

def main():
    """Main function to remove Docker dependencies."""
    remover = DockerRemover()
    
    print("🔥 SOVEREIGN-AI Docker Dependency Removal")
    print("This will modify Agent Zero to run natively without Docker")
    print("and integrate it with REIGN neural core.")
    print()
    
    response = input("Continue? (y/N): ")
    if response.lower() != 'y':
        print("Operation cancelled.")
        return
    
    remover.remove_all_docker_dependencies()
    
    print()
    print("🎉 SOVEREIGN-AI is ready!")
    print("Next steps:")
    print("1. Run: python3 SOVEREIGN_AI_MASTER_INTEGRATION.py")
    print("2. Or run: python3 agent-zero/launch_native.py")
    print("3. Enjoy your Docker-free AI empire!")

if __name__ == "__main__":
    main()
