#!/bin/bash

# Project Obsoletion - Build Test Script
# Author: Augment Agent
# Description: Comprehensive testing script for the neural network bootloader

set -e  # Exit on any error

echo "=== Project Obsoletion Build Test ==="
echo "Testing neural network bootloader build system..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    local missing_deps=()
    
    if ! command -v nasm &> /dev/null; then
        missing_deps+=("nasm")
    fi
    
    if ! command -v ld &> /dev/null; then
        missing_deps+=("binutils")
    fi
    
    if ! command -v qemu-system-i386 &> /dev/null; then
        missing_deps+=("qemu-system-x86")
    fi
    
    if ! command -v make &> /dev/null; then
        missing_deps+=("build-essential")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing dependencies: ${missing_deps[*]}"
        print_status "Run 'make dev-setup' to install dependencies"
        return 1
    fi
    
    print_success "All dependencies found"
    return 0
}

# Test assembly syntax
test_assembly_syntax() {
    print_status "Testing assembly syntax..."
    
    local asm_files=("bootloader.asm" "neural_core.asm" "syscall_hook.asm" "rag_system.asm")
    
    for file in "${asm_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "Assembly file not found: $file"
            return 1
        fi
        
        print_status "Checking syntax: $file"
        
        # Test bootloader (binary format)
        if [ "$file" = "bootloader.asm" ]; then
            if ! nasm -f bin -o /tmp/test_bootloader.bin "$file" 2>/dev/null; then
                print_error "Syntax error in $file"
                return 1
            fi
            rm -f /tmp/test_bootloader.bin
        else
            # Test other files (ELF format)
            if ! nasm -f elf32 -o /tmp/test_"${file%.asm}".o "$file" 2>/dev/null; then
                print_error "Syntax error in $file"
                return 1
            fi
            rm -f /tmp/test_"${file%.asm}".o
        fi
    done
    
    print_success "All assembly files have valid syntax"
    return 0
}

# Test build process
test_build() {
    print_status "Testing build process..."

    # Clean first
    make clean &>/dev/null || true

    # Test UEFI build (primary)
    print_status "Testing UEFI build..."

    print_status "Building UEFI bootloader..."
    if ! make uefi_bootloader.o &>/dev/null; then
        print_error "Failed to build UEFI bootloader"
        return 1
    fi

    print_status "Building UEFI neural core..."
    if ! make uefi_neural_core.o &>/dev/null; then
        print_error "Failed to build UEFI neural core"
        return 1
    fi

    print_status "Building UEFI system hooks..."
    if ! make uefi_system_hooks.o &>/dev/null; then
        print_error "Failed to build UEFI system hooks"
        return 1
    fi

    print_status "Building UEFI service hooks..."
    if ! make uefi_service_hooks.o &>/dev/null; then
        print_error "Failed to build UEFI service hooks"
        return 1
    fi

    print_status "Building UEFI application..."
    if ! make obsoletion.efi &>/dev/null; then
        print_warning "Failed to build UEFI application (may need additional tools)"
        # Continue with legacy build
    else
        print_success "UEFI application built successfully"
    fi

    print_status "Building complete UEFI system..."
    if ! make all &>/dev/null; then
        print_warning "Failed to build complete UEFI system, trying legacy..."

        # Fall back to legacy build
        print_status "Testing legacy build..."

        print_status "Building legacy bootloader..."
        if ! make bootloader.bin &>/dev/null; then
            print_error "Failed to build legacy bootloader"
            return 1
        fi

        print_status "Building legacy neural core..."
        if ! make neural_core.o &>/dev/null; then
            print_error "Failed to build legacy neural core"
            return 1
        fi

        print_status "Building legacy syscall hooks..."
        if ! make syscall_hook.o &>/dev/null; then
            print_error "Failed to build legacy syscall hooks"
            return 1
        fi

        print_status "Building legacy RAG system..."
        if ! make rag_system.o &>/dev/null; then
            print_error "Failed to build legacy RAG system"
            return 1
        fi

        print_status "Building complete legacy system..."
        if ! make legacy &>/dev/null; then
            print_error "Failed to build complete legacy system"
            return 1
        fi

        print_success "Legacy build completed successfully"
    else
        print_success "UEFI build completed successfully"
    fi

    return 0
}

# Test binary sizes
test_binary_sizes() {
    print_status "Testing binary sizes..."
    
    if [ ! -f "bootloader.bin" ]; then
        print_error "Bootloader binary not found"
        return 1
    fi
    
    local bootloader_size=$(stat -c%s bootloader.bin)
    print_status "Bootloader size: $bootloader_size bytes"
    
    if [ $bootloader_size -gt 512 ]; then
        print_error "Bootloader exceeds 512 bytes (MBR limit)"
        return 1
    fi
    
    if [ $bootloader_size -lt 100 ]; then
        print_warning "Bootloader seems unusually small ($bootloader_size bytes)"
    fi
    
    if [ -f "neural_kernel.bin" ]; then
        local kernel_size=$(stat -c%s neural_kernel.bin)
        print_status "Neural kernel size: $kernel_size bytes"
        
        if [ $kernel_size -gt 1048576 ]; then  # 1MB
            print_warning "Neural kernel is quite large ($kernel_size bytes)"
        fi
    fi
    
    if [ -f "obsoletion.img" ]; then
        local image_size=$(stat -c%s obsoletion.img)
        print_status "Disk image size: $image_size bytes"
        
        if [ $image_size -ne 1474560 ]; then  # 1.44MB floppy
            print_warning "Disk image is not standard floppy size"
        fi
    fi
    
    print_success "Binary sizes are acceptable"
    return 0
}

# Test bootloader structure
test_bootloader_structure() {
    print_status "Testing bootloader structure..."
    
    if [ ! -f "bootloader.bin" ]; then
        print_error "Bootloader binary not found"
        return 1
    fi
    
    # Check boot signature (0x55AA at end)
    local signature=$(xxd -s 510 -l 2 -p bootloader.bin)
    if [ "$signature" != "55aa" ]; then
        print_error "Invalid boot signature: $signature (expected 55aa)"
        return 1
    fi
    
    print_success "Bootloader has valid boot signature"
    
    # Check for common x86 instructions at start
    local first_bytes=$(xxd -l 16 -p bootloader.bin | tr -d '\n')
    if [[ ! "$first_bytes" =~ ^[0-9a-f]{32}$ ]]; then
        print_error "Invalid bootloader format"
        return 1
    fi
    
    print_success "Bootloader structure is valid"
    return 0
}

# Test QEMU compatibility
test_qemu_compatibility() {
    print_status "Testing QEMU compatibility..."
    
    if [ ! -f "obsoletion.img" ]; then
        print_error "Disk image not found"
        return 1
    fi
    
    # Test QEMU can start with the image (timeout after 5 seconds)
    print_status "Starting QEMU test (5 second timeout)..."
    
    if timeout 5s qemu-system-i386 -fda obsoletion.img -boot a -nographic -serial stdio &>/dev/null; then
        print_success "QEMU compatibility test passed"
    else
        local exit_code=$?
        if [ $exit_code -eq 124 ]; then
            print_success "QEMU started successfully (timed out as expected)"
        else
            print_error "QEMU failed to start with exit code $exit_code"
            return 1
        fi
    fi
    
    return 0
}

# Test memory layout
test_memory_layout() {
    print_status "Testing memory layout..."
    
    # Check if linker script exists
    if [ ! -f "linker.ld" ]; then
        print_error "Linker script not found"
        return 1
    fi
    
    # Verify linker script syntax
    if ! ld -T linker.ld --help &>/dev/null; then
        print_warning "Could not validate linker script"
    else
        print_success "Linker script is valid"
    fi
    
    return 0
}

# Generate test report
generate_report() {
    print_status "Generating test report..."
    
    local report_file="test_report.txt"
    
    cat > "$report_file" << EOF
Project Obsoletion - Build Test Report
Generated: $(date)

=== Build Information ===
EOF
    
    if [ -f "bootloader.bin" ]; then
        echo "Bootloader size: $(stat -c%s bootloader.bin) bytes" >> "$report_file"
    fi
    
    if [ -f "neural_kernel.bin" ]; then
        echo "Neural kernel size: $(stat -c%s neural_kernel.bin) bytes" >> "$report_file"
    fi
    
    if [ -f "obsoletion.img" ]; then
        echo "Disk image size: $(stat -c%s obsoletion.img) bytes" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

=== System Information ===
OS: $(uname -s)
Architecture: $(uname -m)
NASM version: $(nasm -v 2>/dev/null || echo "Not found")
LD version: $(ld --version 2>/dev/null | head -n1 || echo "Not found")
QEMU version: $(qemu-system-i386 --version 2>/dev/null | head -n1 || echo "Not found")

=== File Checksums ===
EOF
    
    for file in *.asm *.bin *.img Makefile linker.ld; do
        if [ -f "$file" ]; then
            echo "$file: $(md5sum "$file" | cut -d' ' -f1)" >> "$report_file"
        fi
    done
    
    print_success "Test report generated: $report_file"
}

# Main test execution
main() {
    echo "Starting comprehensive build test..."
    echo ""
    
    local tests=(
        "check_dependencies"
        "test_assembly_syntax"
        "test_build"
        "test_binary_sizes"
        "test_bootloader_structure"
        "test_memory_layout"
        "test_qemu_compatibility"
    )
    
    local passed=0
    local failed=0
    
    for test in "${tests[@]}"; do
        echo ""
        if $test; then
            ((passed++))
        else
            ((failed++))
            print_error "Test failed: $test"
        fi
    done
    
    echo ""
    echo "=== Test Summary ==="
    print_status "Tests passed: $passed"
    if [ $failed -gt 0 ]; then
        print_error "Tests failed: $failed"
    else
        print_success "All tests passed!"
    fi
    
    generate_report
    
    echo ""
    if [ $failed -eq 0 ]; then
        print_success "Project Obsoletion build test completed successfully!"
        echo ""
        print_status "Next steps:"
        echo "  - Run 'make test' to test in QEMU"
        echo "  - Run 'make debug' for debugging"
        echo "  - See README.md for installation options"
        return 0
    else
        print_error "Some tests failed. Please fix the issues before proceeding."
        return 1
    fi
}

# Run main function
main "$@"
