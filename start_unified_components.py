#!/usr/bin/env python3
"""
Start Unified SOVEREIGN-AI Components
Launches all integrated AI systems alongside QEMU
"""

import subprocess
import time
import threading
import os
import signal
import sys

class UnifiedAIBootstrap:
    def __init__(self):
        self.processes = []
        self.running = True
    
    def log(self, message):
        print(f"[UNIFIED-AI] {message}")
    
    def start_integration_bridge(self):
        """Start the REIGN integration bridge."""
        try:
            self.log("Starting REIGN integration bridge...")
            process = subprocess.Popen([
                'python3', 'reign_integration_bridge.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            self.processes.append(process)
            time.sleep(2)
            self.log("✓ REIGN integration bridge started")
            return True
        except Exception as e:
            self.log(f"✗ Failed to start integration bridge: {e}")
            return False
    
    def start_unified_interface(self):
        """Start the unified AI interface."""
        try:
            self.log("Starting Unified SOVEREIGN-AI interface...")
            process = subprocess.Popen([
                'python3', 'unified_sovereign_ai.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            self.processes.append(process)
            time.sleep(3)
            self.log("✓ Unified interface started")
            return True
        except Exception as e:
            self.log(f"✗ Failed to start unified interface: {e}")
            return False
    
    def start_silly_tavern(self):
        """Start SillyTavern if available."""
        if os.path.exists("silly-tavern/server.js"):
            try:
                self.log("Starting SillyTavern base platform...")
                os.chdir("silly-tavern")
                
                # Install dependencies if needed
                if not os.path.exists("node_modules"):
                    self.log("Installing SillyTavern dependencies...")
                    subprocess.run(["npm", "install"], check=True)
                
                process = subprocess.Popen([
                    "node", "server.js"
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                self.processes.append(process)
                os.chdir("..")
                time.sleep(3)
                self.log("✓ SillyTavern started")
                return True
            except Exception as e:
                os.chdir("..")
                self.log(f"✗ Failed to start SillyTavern: {e}")
                return False
        else:
            self.log("⚠ SillyTavern not found - skipping")
            return True
    
    def monitor_processes(self):
        """Monitor all processes."""
        self.log("Monitoring UNIFIED SOVEREIGN-AI processes...")
        
        while self.running:
            time.sleep(5)
            
            # Check if any process has died
            for i, process in enumerate(self.processes):
                if process.poll() is not None:
                    self.log(f"Process {i} has stopped")
            
            # Check if QEMU is still running (parent process)
            try:
                os.kill(os.getppid(), 0)
            except OSError:
                self.log("QEMU has stopped - shutting down components")
                self.shutdown()
                break
    
    def shutdown(self):
        """Shutdown all components."""
        self.log("Shutting down UNIFIED SOVEREIGN-AI components...")
        self.running = False
        
        for process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                try:
                    process.kill()
                except:
                    pass
        
        self.log("✓ All components shut down")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        self.log("Received shutdown signal")
        self.shutdown()
        sys.exit(0)
    
    def run(self):
        """Run the unified AI bootstrap."""
        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.log("🔥 UNIFIED SOVEREIGN-AI Bootstrap Starting...")
        self.log("=" * 50)
        
        # Start components in sequence
        success = True
        
        if not self.start_integration_bridge():
            success = False
        
        if not self.start_silly_tavern():
            success = False
        
        if not self.start_unified_interface():
            success = False
        
        if success:
            self.log("=" * 50)
            self.log("🎉 UNIFIED SOVEREIGN-AI FULLY OPERATIONAL!")
            self.log("All AI systems integrated and running!")
            self.log("Access via:")
            self.log("  - Unified Interface: Python GUI")
            self.log("  - SillyTavern: http://localhost:8000")
            self.log("  - Integration API: http://localhost:5001")
            self.log("=" * 50)
            
            # Monitor processes
            self.monitor_processes()
        else:
            self.log("✗ Failed to start some components")
            self.shutdown()

if __name__ == "__main__":
    bootstrap = UnifiedAIBootstrap()
    bootstrap.run()
