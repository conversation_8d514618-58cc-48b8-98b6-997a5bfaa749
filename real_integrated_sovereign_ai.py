#!/usr/bin/env python3
"""
REAL INTEGRATED SOVEREIGN-AI
Actual code harvesting and integration from the real codebases
"""

import os
import sys
import json
import time
import threading
import subprocess
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import sqlite3
import uuid
from datetime import datetime
import requests
import websocket
import signal
from pathlib import Path
import shutil
import importlib.util

class RealIntegratedSovereignAI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("REAL INTEGRATED SOVEREIGN-AI: Actual Code Integration")
        self.root.geometry("1800x1200")
        self.root.configure(bg='#0a0a0a')
        
        # Core processes and servers
        self.reign_process = None
        self.agent_zero_server = None
        self.silly_tavern_server = None
        self.code_companion_server = None
        
        # Real integration components
        self.agent_zero_api = None
        self.silly_tavern_api = None
        self.code_companion_api = None
        
        # Data storage
        self.active_agents = {}
        self.characters = {}
        self.chat_sessions = {}
        self.code_sessions = {}
        
        # Server ports
        self.agent_zero_port = 8080
        self.silly_tavern_port = 8000
        self.code_companion_port = 8081
        
        self.setup_ui()
        self.harvest_and_integrate_real_code()
    
    def setup_ui(self):
        """Setup the real integrated UI."""
        # Title
        title_label = tk.Label(self.root,
                              text="REAL INTEGRATED SOVEREIGN-AI",
                              font=('Courier', 24, 'bold'),
                              fg='#ff0066', bg='#0a0a0a')
        title_label.pack(pady=10)
        
        subtitle_label = tk.Label(self.root,
                                 text="🧠 REIGN + 🤖 Real Agent Zero + 💬 Real SillyTavern + 💻 Real Code Companion",
                                 font=('Courier', 14),
                                 fg='#ffffff', bg='#0a0a0a')
        subtitle_label.pack()
        
        # Create notebook with real integration tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Setup all real integration tabs
        self.setup_reign_tab()
        self.setup_real_agent_zero_tab()
        self.setup_real_silly_tavern_tab()
        self.setup_real_code_companion_tab()
        self.setup_integration_monitor_tab()
        
        # Status bar with real server status
        self.setup_status_bar()
    
    def harvest_and_integrate_real_code(self):
        """Harvest and integrate actual code from the real projects."""
        self.log_to_integration("🔄 Harvesting real code from actual projects...")
        
        # Check if projects exist
        projects = {
            'agent-zero': './agent-zero',
            'silly-tavern': './silly-tavern', 
            'code-companion': './code-companion'
        }
        
        for name, path in projects.items():
            if os.path.exists(path):
                self.log_to_integration(f"✅ Found {name} at {path}")
            else:
                self.log_to_integration(f"❌ Missing {name} at {path}")
        
        # Initialize real integrations
        self.init_real_agent_zero()
        self.init_real_silly_tavern()
        self.init_real_code_companion()
        
        self.log_to_integration("✅ Real code harvesting complete!")
    
    def init_real_agent_zero(self):
        """Initialize real Agent Zero integration."""
        try:
            self.log_to_integration("🤖 Initializing real Agent Zero...")
            
            # Check if Agent Zero exists
            agent_zero_path = "./agent-zero"
            if not os.path.exists(agent_zero_path):
                self.log_to_integration("❌ Agent Zero directory not found")
                return
            
            # Import Agent Zero's actual modules
            sys.path.insert(0, agent_zero_path)
            
            # Start Agent Zero server using their actual run_ui.py
            self.start_real_agent_zero_server()
            
            self.log_to_integration("✅ Real Agent Zero integration initialized")
            
        except Exception as e:
            self.log_to_integration(f"❌ Agent Zero integration failed: {e}")
    
    def start_real_agent_zero_server(self):
        """Start the actual Agent Zero server."""
        try:
            agent_zero_path = "./agent-zero"
            
            # Start Agent Zero using their actual startup script
            self.agent_zero_server = subprocess.Popen([
                sys.executable, "run_ui.py"
            ], cwd=agent_zero_path, 
               stdout=subprocess.PIPE, 
               stderr=subprocess.PIPE,
               universal_newlines=True)
            
            self.log_to_integration("🚀 Agent Zero server started")
            
            # Monitor Agent Zero output
            threading.Thread(target=self.monitor_agent_zero_output, daemon=True).start()
            
        except Exception as e:
            self.log_to_integration(f"❌ Failed to start Agent Zero server: {e}")
    
    def monitor_agent_zero_output(self):
        """Monitor real Agent Zero server output."""
        try:
            while self.agent_zero_server and self.agent_zero_server.poll() is None:
                line = self.agent_zero_server.stdout.readline()
                if line:
                    self.root.after(0, lambda l=line: self.log_to_agent_zero(f"[A0] {l.strip()}"))
                time.sleep(0.1)
        except Exception as e:
            self.root.after(0, lambda: self.log_to_integration(f"Agent Zero monitor error: {e}"))
    
    def init_real_silly_tavern(self):
        """Initialize real SillyTavern integration."""
        try:
            self.log_to_integration("💬 Initializing real SillyTavern...")
            
            # Check if SillyTavern exists
            silly_tavern_path = "./silly-tavern"
            if not os.path.exists(silly_tavern_path):
                self.log_to_integration("❌ SillyTavern directory not found")
                return
            
            # Start SillyTavern server using their actual server.js
            self.start_real_silly_tavern_server()
            
            self.log_to_integration("✅ Real SillyTavern integration initialized")
            
        except Exception as e:
            self.log_to_integration(f"❌ SillyTavern integration failed: {e}")
    
    def start_real_silly_tavern_server(self):
        """Start the actual SillyTavern server."""
        try:
            silly_tavern_path = "./silly-tavern"
            
            # Check if node_modules exists, install if not
            if not os.path.exists(os.path.join(silly_tavern_path, "node_modules")):
                self.log_to_integration("📦 Installing SillyTavern dependencies...")
                subprocess.run(["npm", "install"], cwd=silly_tavern_path, check=True)
            
            # Start SillyTavern using their actual server.js
            self.silly_tavern_server = subprocess.Popen([
                "node", "server.js", "--listen", "--port", str(self.silly_tavern_port)
            ], cwd=silly_tavern_path,
               stdout=subprocess.PIPE,
               stderr=subprocess.PIPE,
               universal_newlines=True)
            
            self.log_to_integration("🚀 SillyTavern server started")
            
            # Monitor SillyTavern output
            threading.Thread(target=self.monitor_silly_tavern_output, daemon=True).start()
            
        except Exception as e:
            self.log_to_integration(f"❌ Failed to start SillyTavern server: {e}")
    
    def monitor_silly_tavern_output(self):
        """Monitor real SillyTavern server output."""
        try:
            while self.silly_tavern_server and self.silly_tavern_server.poll() is None:
                line = self.silly_tavern_server.stdout.readline()
                if line:
                    self.root.after(0, lambda l=line: self.log_to_silly_tavern(f"[ST] {l.strip()}"))
                time.sleep(0.1)
        except Exception as e:
            self.root.after(0, lambda: self.log_to_integration(f"SillyTavern monitor error: {e}"))
    
    def init_real_code_companion(self):
        """Initialize real Code Companion integration."""
        try:
            self.log_to_integration("💻 Initializing real Code Companion...")
            
            # Check if Code Companion exists
            code_companion_path = "./code-companion"
            if not os.path.exists(code_companion_path):
                self.log_to_integration("❌ Code Companion directory not found")
                return
            
            # Code Companion is a Neovim plugin, so we'll create a bridge
            self.setup_code_companion_bridge()
            
            self.log_to_integration("✅ Real Code Companion integration initialized")
            
        except Exception as e:
            self.log_to_integration(f"❌ Code Companion integration failed: {e}")
    
    def setup_code_companion_bridge(self):
        """Setup bridge to Code Companion functionality."""
        try:
            # Create a Python bridge to Code Companion's Lua functionality
            code_companion_path = "./code-companion"
            
            # Read Code Companion's actual configuration and features
            lua_files = []
            for root, dirs, files in os.walk(os.path.join(code_companion_path, "lua")):
                for file in files:
                    if file.endswith(".lua"):
                        lua_files.append(os.path.join(root, file))
            
            self.log_to_integration(f"📁 Found {len(lua_files)} Code Companion Lua files")
            
            # Parse main functionality
            init_file = os.path.join(code_companion_path, "lua/codecompanion/init.lua")
            if os.path.exists(init_file):
                with open(init_file, 'r') as f:
                    content = f.read()
                    # Extract function names and capabilities
                    functions = []
                    for line in content.split('\n'):
                        if 'CodeCompanion.' in line and 'function' in line:
                            functions.append(line.strip())
                    
                    self.log_to_integration(f"🔧 Found {len(functions)} Code Companion functions")
            
        except Exception as e:
            self.log_to_integration(f"❌ Code Companion bridge setup failed: {e}")
    
    def setup_reign_tab(self):
        """Setup REIGN neural core tab."""
        self.reign_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(self.reign_frame, text='🧠 REIGN Neural Core')
        
        # Title
        title_label = tk.Label(self.reign_frame,
                              text="REIGN NEURAL CORE CONTROL",
                              font=('Courier', 18, 'bold'),
                              fg='#ff6600', bg='#1a1a1a')
        title_label.pack(pady=10)
        
        # Main container
        main_container = tk.Frame(self.reign_frame, bg='#1a1a1a')
        main_container.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Left panel - Controls
        control_frame = tk.LabelFrame(main_container, text="Neural Core Controls",
                                    font=('Courier', 12, 'bold'),
                                    fg='#00ff00', bg='#1a1a1a')
        control_frame.pack(side='left', fill='y', padx=(0, 10))
        
        # EFI file selection
        tk.Label(control_frame, text="REIGN EFI File:", 
                font=('Courier', 10), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=5)
        
        self.efi_var = tk.StringVar(value="obsoletion_enhanced.efi")
        efi_entry = tk.Entry(control_frame, textvariable=self.efi_var,
                           bg='#2a2a2a', fg='#ffffff', font=('Courier', 9))
        efi_entry.pack(fill='x', pady=5)
        
        # Memory and CPU settings
        tk.Label(control_frame, text="Memory (MB):", 
                font=('Courier', 10), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=5)
        
        self.memory_var = tk.StringVar(value="4096")
        memory_entry = tk.Entry(control_frame, textvariable=self.memory_var,
                              bg='#2a2a2a', fg='#ffffff', font=('Courier', 9))
        memory_entry.pack(fill='x', pady=5)
        
        tk.Label(control_frame, text="CPU Cores:", 
                font=('Courier', 10), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=5)
        
        self.cpu_var = tk.StringVar(value="4")
        cpu_entry = tk.Entry(control_frame, textvariable=self.cpu_var,
                           bg='#2a2a2a', fg='#ffffff', font=('Courier', 9))
        cpu_entry.pack(fill='x', pady=5)
        
        # Control buttons
        button_frame = tk.Frame(control_frame, bg='#1a1a1a')
        button_frame.pack(fill='x', pady=20)
        
        self.start_reign_btn = tk.Button(button_frame, text="🚀 START REIGN",
                                       command=self.start_reign_core,
                                       bg='#ff6600', fg='#ffffff',
                                       font=('Arial', 12, 'bold'))
        self.start_reign_btn.pack(fill='x', pady=5)
        
        self.stop_reign_btn = tk.Button(button_frame, text="⏹️ STOP REIGN",
                                      command=self.stop_reign_core,
                                      bg='#666666', fg='#ffffff',
                                      font=('Arial', 12, 'bold'),
                                      state='disabled')
        self.stop_reign_btn.pack(fill='x', pady=5)
        
        # Right panel - Output
        output_frame = tk.LabelFrame(main_container, text="Neural Network Output",
                                   font=('Courier', 12, 'bold'),
                                   fg='#00ff00', bg='#1a1a1a')
        output_frame.pack(side='right', fill='both', expand=True)
        
        self.reign_output = scrolledtext.ScrolledText(output_frame,
                                                    bg='#000000', fg='#00ff00',
                                                    font=('Courier', 10),
                                                    wrap='word')
        self.reign_output.pack(fill='both', expand=True, padx=5, pady=5)

    def setup_real_agent_zero_tab(self):
        """Setup real Agent Zero integration tab."""
        self.agent_zero_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(self.agent_zero_frame, text='🤖 Real Agent Zero')

        # Title
        title_label = tk.Label(self.agent_zero_frame,
                              text="REAL AGENT ZERO INTEGRATION",
                              font=('Courier', 18, 'bold'),
                              fg='#00aa00', bg='#1a1a1a')
        title_label.pack(pady=10)

        # Main container
        main_container = tk.Frame(self.agent_zero_frame, bg='#1a1a1a')
        main_container.pack(fill='both', expand=True, padx=20, pady=10)

        # Left panel - Real Agent Zero controls
        control_frame = tk.LabelFrame(main_container, text="Agent Zero Server Control",
                                    font=('Courier', 12, 'bold'),
                                    fg='#00aa00', bg='#1a1a1a')
        control_frame.pack(side='left', fill='y', padx=(0, 10))

        # Server controls
        tk.Button(control_frame, text="🚀 Start Agent Zero Server",
                 command=self.start_real_agent_zero_server,
                 bg='#00aa00', fg='#ffffff',
                 font=('Arial', 10, 'bold')).pack(fill='x', pady=5)

        tk.Button(control_frame, text="⏹️ Stop Agent Zero Server",
                 command=self.stop_agent_zero_server,
                 bg='#aa0000', fg='#ffffff',
                 font=('Arial', 10, 'bold')).pack(fill='x', pady=5)

        tk.Button(control_frame, text="🌐 Open Agent Zero UI",
                 command=self.open_agent_zero_ui,
                 bg='#0066aa', fg='#ffffff',
                 font=('Arial', 10, 'bold')).pack(fill='x', pady=5)

        # Agent creation using real Agent Zero API
        tk.Label(control_frame, text="Create Real Agent:",
                font=('Courier', 10, 'bold'), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=(20, 5))

        self.real_agent_name_var = tk.StringVar()
        tk.Entry(control_frame, textvariable=self.real_agent_name_var,
                bg='#2a2a2a', fg='#ffffff', font=('Courier', 9)).pack(fill='x', pady=2)

        tk.Button(control_frame, text="🤖 Create via Agent Zero API",
                 command=self.create_real_agent,
                 bg='#006600', fg='#ffffff').pack(fill='x', pady=5)

        # Real Agent Zero task interface
        tk.Label(control_frame, text="Agent Zero Task:",
                font=('Courier', 10, 'bold'), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=(20, 5))

        self.agent_task_var = tk.StringVar()
        task_entry = tk.Entry(control_frame, textvariable=self.agent_task_var,
                            bg='#2a2a2a', fg='#ffffff', font=('Courier', 9))
        task_entry.pack(fill='x', pady=2)
        task_entry.bind('<Return>', lambda e: self.send_task_to_agent_zero())

        tk.Button(control_frame, text="📋 Send Task to Agent Zero",
                 command=self.send_task_to_agent_zero,
                 bg='#aa6600', fg='#ffffff').pack(fill='x', pady=5)

        # Right panel - Real Agent Zero output
        output_frame = tk.LabelFrame(main_container, text="Real Agent Zero Output",
                                   font=('Courier', 12, 'bold'),
                                   fg='#00aa00', bg='#1a1a1a')
        output_frame.pack(side='right', fill='both', expand=True)

        self.agent_zero_output = scrolledtext.ScrolledText(output_frame,
                                                         bg='#000000', fg='#00ff00',
                                                         font=('Courier', 10),
                                                         wrap='word')
        self.agent_zero_output.pack(fill='both', expand=True, padx=5, pady=5)

    def setup_real_silly_tavern_tab(self):
        """Setup real SillyTavern integration tab."""
        self.silly_tavern_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(self.silly_tavern_frame, text='💬 Real SillyTavern')

        # Title
        title_label = tk.Label(self.silly_tavern_frame,
                              text="REAL SILLYTAVERN INTEGRATION",
                              font=('Courier', 18, 'bold'),
                              fg='#ff00ff', bg='#1a1a1a')
        title_label.pack(pady=10)

        # Main container
        main_container = tk.Frame(self.silly_tavern_frame, bg='#1a1a1a')
        main_container.pack(fill='both', expand=True, padx=20, pady=10)

        # Left panel - Real SillyTavern controls
        control_frame = tk.LabelFrame(main_container, text="SillyTavern Server Control",
                                    font=('Courier', 12, 'bold'),
                                    fg='#ff00ff', bg='#1a1a1a')
        control_frame.pack(side='left', fill='y', padx=(0, 10))

        # Server controls
        tk.Button(control_frame, text="🚀 Start SillyTavern Server",
                 command=self.start_real_silly_tavern_server,
                 bg='#ff00ff', fg='#ffffff',
                 font=('Arial', 10, 'bold')).pack(fill='x', pady=5)

        tk.Button(control_frame, text="⏹️ Stop SillyTavern Server",
                 command=self.stop_silly_tavern_server,
                 bg='#aa0000', fg='#ffffff',
                 font=('Arial', 10, 'bold')).pack(fill='x', pady=5)

        tk.Button(control_frame, text="🌐 Open SillyTavern UI",
                 command=self.open_silly_tavern_ui,
                 bg='#0066aa', fg='#ffffff',
                 font=('Arial', 10, 'bold')).pack(fill='x', pady=5)

        # Real SillyTavern API controls
        tk.Label(control_frame, text="SillyTavern API:",
                font=('Courier', 10, 'bold'), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=(20, 5))

        tk.Button(control_frame, text="📥 Load Characters",
                 command=self.load_real_characters,
                 bg='#6600aa', fg='#ffffff').pack(fill='x', pady=2)

        tk.Button(control_frame, text="💬 Start Chat Session",
                 command=self.start_real_chat_session,
                 bg='#aa0066', fg='#ffffff').pack(fill='x', pady=2)

        tk.Button(control_frame, text="🎭 Import from Chub",
                 command=self.import_from_real_chub,
                 bg='#0066aa', fg='#ffffff').pack(fill='x', pady=2)

        # Real chat interface
        tk.Label(control_frame, text="Chat with REIGN:",
                font=('Courier', 10, 'bold'), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=(20, 5))

        self.real_chat_input = scrolledtext.ScrolledText(control_frame,
                                                       bg='#2a2a2a', fg='#ffffff',
                                                       font=('Courier', 9), height=3)
        self.real_chat_input.pack(fill='x', pady=2)

        tk.Button(control_frame, text="💬 Send to SillyTavern",
                 command=self.send_real_chat_message,
                 bg='#ff00ff', fg='#ffffff').pack(fill='x', pady=5)

        # Right panel - Real SillyTavern output
        output_frame = tk.LabelFrame(main_container, text="Real SillyTavern Output",
                                   font=('Courier', 12, 'bold'),
                                   fg='#ff00ff', bg='#1a1a1a')
        output_frame.pack(side='right', fill='both', expand=True)

        self.silly_tavern_output = scrolledtext.ScrolledText(output_frame,
                                                           bg='#000000', fg='#ff00ff',
                                                           font=('Courier', 10),
                                                           wrap='word')
        self.silly_tavern_output.pack(fill='both', expand=True, padx=5, pady=5)

    def setup_real_code_companion_tab(self):
        """Setup real Code Companion integration tab."""
        self.code_companion_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(self.code_companion_frame, text='💻 Real Code Companion')

        # Title
        title_label = tk.Label(self.code_companion_frame,
                              text="REAL CODE COMPANION INTEGRATION",
                              font=('Courier', 18, 'bold'),
                              fg='#0066ff', bg='#1a1a1a')
        title_label.pack(pady=10)

        # Main container
        main_container = tk.Frame(self.code_companion_frame, bg='#1a1a1a')
        main_container.pack(fill='both', expand=True, padx=20, pady=10)

        # Left panel - Real Code Companion controls
        control_frame = tk.LabelFrame(main_container, text="Code Companion Bridge Control",
                                    font=('Courier', 12, 'bold'),
                                    fg='#0066ff', bg='#1a1a1a')
        control_frame.pack(side='left', fill='y', padx=(0, 10))

        # Code Companion features
        tk.Button(control_frame, text="🔧 Initialize Code Companion",
                 command=self.init_real_code_companion,
                 bg='#0066ff', fg='#ffffff',
                 font=('Arial', 10, 'bold')).pack(fill='x', pady=5)

        tk.Button(control_frame, text="📁 Load Code Companion Config",
                 command=self.load_code_companion_config,
                 bg='#006600', fg='#ffffff').pack(fill='x', pady=5)

        # Real Code Companion functions
        tk.Label(control_frame, text="Code Companion Functions:",
                font=('Courier', 10, 'bold'), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=(20, 5))

        tk.Button(control_frame, text="💡 AI Code Completion",
                 command=self.real_code_completion,
                 bg='#0066aa', fg='#ffffff').pack(fill='x', pady=2)

        tk.Button(control_frame, text="💬 Start Chat Session",
                 command=self.real_code_chat,
                 bg='#aa0066', fg='#ffffff').pack(fill='x', pady=2)

        tk.Button(control_frame, text="📖 Code Explanation",
                 command=self.real_code_explanation,
                 bg='#006600', fg='#ffffff').pack(fill='x', pady=2)

        tk.Button(control_frame, text="🔧 Code Refactoring",
                 command=self.real_code_refactor,
                 bg='#aa6600', fg='#ffffff').pack(fill='x', pady=2)

        # Code input area
        tk.Label(control_frame, text="Code Input:",
                font=('Courier', 10, 'bold'), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=(20, 5))

        self.real_code_input = scrolledtext.ScrolledText(control_frame,
                                                       bg='#2a2a2a', fg='#00ff00',
                                                       font=('Courier', 9), height=8)
        self.real_code_input.pack(fill='x', pady=2)

        # Right panel - Real Code Companion output
        output_frame = tk.LabelFrame(main_container, text="Real Code Companion Output",
                                   font=('Courier', 12, 'bold'),
                                   fg='#0066ff', bg='#1a1a1a')
        output_frame.pack(side='right', fill='both', expand=True)

        self.code_companion_output = scrolledtext.ScrolledText(output_frame,
                                                             bg='#000000', fg='#0066ff',
                                                             font=('Courier', 10),
                                                             wrap='word')
        self.code_companion_output.pack(fill='both', expand=True, padx=5, pady=5)

    def setup_integration_monitor_tab(self):
        """Setup integration monitoring tab."""
        self.integration_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(self.integration_frame, text='📊 Integration Monitor')

        # Title
        title_label = tk.Label(self.integration_frame,
                              text="REAL INTEGRATION MONITOR",
                              font=('Courier', 18, 'bold'),
                              fg='#ffff00', bg='#1a1a1a')
        title_label.pack(pady=10)

        # Integration status
        self.integration_output = scrolledtext.ScrolledText(self.integration_frame,
                                                          bg='#000000', fg='#ffff00',
                                                          font=('Courier', 10),
                                                          wrap='word')
        self.integration_output.pack(fill='both', expand=True, padx=20, pady=10)

    def setup_status_bar(self):
        """Setup status bar with real server status."""
        self.status_frame = tk.Frame(self.root, bg='#2a2a2a', relief='sunken', bd=2)
        self.status_frame.pack(fill='x', side='bottom')

        self.status_label = tk.Label(self.status_frame,
                                    text="REAL INTEGRATED SOVEREIGN-AI Ready",
                                    font=('Courier', 12),
                                    fg='#00ff00', bg='#2a2a2a')
        self.status_label.pack(side='left', padx=10, pady=5)

        # Real server status indicators
        self.reign_status = tk.Label(self.status_frame,
                                   text="REIGN: Offline",
                                   font=('Courier', 10),
                                   fg='#ff0000', bg='#2a2a2a')
        self.reign_status.pack(side='right', padx=10, pady=5)

        self.agent_zero_status = tk.Label(self.status_frame,
                                        text="Agent Zero: Offline",
                                        font=('Courier', 10),
                                        fg='#ff0000', bg='#2a2a2a')
        self.agent_zero_status.pack(side='right', padx=10, pady=5)

        self.silly_tavern_status = tk.Label(self.status_frame,
                                          text="SillyTavern: Offline",
                                          font=('Courier', 10),
                                          fg='#ff0000', bg='#2a2a2a')
        self.silly_tavern_status.pack(side='right', padx=10, pady=5)

        self.code_companion_status = tk.Label(self.status_frame,
                                            text="Code Companion: Offline",
                                            font=('Courier', 10),
                                            fg='#ff0000', bg='#2a2a2a')
        self.code_companion_status.pack(side='right', padx=10, pady=5)
