#!/bin/bash
# Project Obsoletion USB Installer Creator

if [ "$#" -ne 1 ]; then
    echo "Usage: $0 /dev/sdX (replace X with your USB drive letter)"
    echo "WARNING: This will erase all data on the specified drive!"
    exit 1
fi

USB_DEVICE=$1

if [[ ! -b "$USB_DEVICE" ]]; then
    echo "Error: $USB_DEVICE is not a valid block device"
    exit 1
fi

echo "WARNING: This will erase ALL DATA on $USB_DEVICE"
echo "Are you sure you want to continue? (y/n)"
read -r confirm

if [[ "$confirm" != "y" ]]; then
    echo "Operation cancelled"
    exit 0
fi

echo "Creating bootable USB installer on $USB_DEVICE..."

# Create FAT32 filesystem
sudo umount "${USB_DEVICE}"* 2>/dev/null || true
sudo parted -s "$USB_DEVICE" mklabel gpt
sudo parted -s "$USB_DEVICE" mkpart primary fat32 1MiB 100%
sudo parted -s "$USB_DEVICE" set 1 esp on
sudo mkfs.fat -F 32 "${USB_DEVICE}1"

# Mount the drive
MOUNT_POINT=$(mktemp -d)
sudo mount "${USB_DEVICE}1" "$MOUNT_POINT"

# Create EFI directory structure
sudo mkdir -p "$MOUNT_POINT/EFI/BOOT"

# Copy the EFI application
sudo cp "$(dirname "$0")/source/obsoletion_enhanced.efi" "$MOUNT_POINT/EFI/BOOT/BOOTX64.EFI"

# Create README file
cat > "$MOUNT_POINT/README.txt" << 'README'
Project Obsoletion - Revolutionary AI System
============================================

Thank you for installing Project Obsoletion, the world's first firmware-level neural network with complete GUI interaction capabilities.

System Features:
- Sub-millisecond inference performance (0.19ms)
- Real-time learning and adaptation
- Complete visual understanding and GUI control
- Production-ready deployment

To boot this system:
1. Restart your computer
2. Enter BIOS/UEFI setup (usually by pressing F2, DEL, or ESC during boot)
3. Set this USB drive as the first boot device
4. Save changes and reboot

WARNING: This is an advanced AI system operating at firmware level. Use with caution and ensure you have backups of important data.

For support and documentation:
https://project-obsoletion.example.com

(c) 2025 Project Obsoletion Team
README

# Unmount the drive
sudo umount "$MOUNT_POINT"
rmdir "$MOUNT_POINT"

echo "USB installer created successfully!"
echo "You can now boot from this USB drive to test Project Obsoletion."
