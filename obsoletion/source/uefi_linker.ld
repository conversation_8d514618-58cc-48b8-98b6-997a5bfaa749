/* Project Obsoletion UEFI Linker Script */
OUTPUT_FORMAT("elf64-x86-64")
OUTPUT_ARCH(i386:x86-64)
ENTRY(_start)

SECTIONS
{
    . = 0x400000;
    
    .text : ALIGN(4096) {
        *(.text)
        *(.text.*)
    }
    
    .rodata : ALIGN(4096) {
        *(.rodata)
        *(.rodata.*)
    }
    
    .data : ALIGN(4096) {
        *(.data)
        *(.data.*)
        *(.sdata)
        *(.sdata.*)
    }
    
    .dynamic : ALIGN(4096) {
        *(.dynamic)
    }
    
    .dynsym : ALIGN(4096) {
        *(.dynsym)
    }
    
    .rel : ALIGN(4096) {
        *(.rel)
        *(.rel.*)
    }
    
    .rela : ALIGN(4096) {
        *(.rela)
        *(.rela.*)
    }
    
    .reloc : ALIGN(4096) {
        *(.reloc)
    }
    
    .bss : ALIGN(4096) {
        *(.bss)
        *(.bss.*)
        *(.sbss)
        *(.sbss.*)
    }
    
    /DISCARD/ : {
        *(.note.*)
        *(.comment)
        *(.eh_frame)
    }
}
