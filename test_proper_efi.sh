#!/bin/bash
# Test the properly built EFI application

echo "╔══════════════════════════════════════════════════════════════╗"
echo "║              TESTING PROPERLY BUILT EFI APPLICATION         ║"
echo "║           This should actually work unlike the old ones!    ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo ""

cd "/home/<USER>/project 1"

# Check if the EFI file exists
if [[ ! -f "test_working_efi.efi" ]]; then
    echo "✗ test_working_efi.efi not found!"
    exit 1
fi

echo "✓ Found properly built EFI: test_working_efi.efi ($(stat -c%s test_working_efi.efi) bytes)"

# Create test directory
mkdir -p test_proper_efi/EFI/BOOT
cp test_working_efi.efi test_proper_efi/EFI/BOOT/BOOTX64.EFI
cp test_working_efi.efi test_proper_efi/test_working_efi.efi

# Create simple startup script
cat > test_proper_efi/startup.nsh << 'EOF'
cls
echo Testing Properly Built EFI Application
echo =======================================
echo.
echo This EFI was built with gnu-efi and should work correctly!
echo.
echo Attempting to execute test_working_efi.efi...
test_working_efi.efi
echo.
echo If you saw the Project Obsoletion message above, the build is FIXED!
echo.
echo Manual execution test:
echo Type: test_working_efi.efi
EOF

echo "Starting QEMU with properly built EFI test..."
echo "If this works, we've solved the build problem!"
echo ""

qemu-system-x86_64 \
    -bios /usr/share/ovmf/OVMF.fd \
    -drive format=raw,file=fat:rw:test_proper_efi \
    -m 1024 \
    -smp 1 \
    -boot order=c \
    -boot menu=off \
    -serial stdio \
    -monitor none \
    -nographic \
    -no-reboot

# Cleanup
rm -rf test_proper_efi

echo ""
echo "Test completed."
