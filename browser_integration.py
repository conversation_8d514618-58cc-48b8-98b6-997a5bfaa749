
# Browser Integration for SOVEREIGN-AI
import webbrowser
import requests
from urllib.parse import urlparse

class BrowserIntegration:
    def __init__(self):
        self.current_url = None
        self.history = []

    def navigate_to(self, url):
        """Navigate to URL."""
        try:
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url

            webbrowser.open(url)
            self.current_url = url
            self.history.append(url)
            return f"Navigated to: {url}"
        except Exception as e:
            return f"Navigation failed: {str(e)}"

    def get_page_content(self, url):
        """Get page content for AI analysis."""
        try:
            response = requests.get(url, timeout=10)
            return response.text[:5000]  # Limit content
        except Exception as e:
            return f"Failed to get content: {str(e)}"

    def search_web(self, query):
        """Search the web."""
        search_url = f"https://www.google.com/search?q={query}"
        return self.navigate_to(search_url)

# Global browser instance
browser_integration = BrowserIntegration()
