#!/bin/bash
# Launch INTEGRATED SOVEREIGN-AI
# The ultimate AI interface with Project Obsoletion GUI integration

echo "🔥 LAUNCHING INTEGRATED SOVEREIGN-AI 🔥"
echo "========================================"
echo "🧠 Project Obsoletion GUI Integration"
echo "💬 SillyTavern Chat Interface"
echo "💻 Code Companion AI Assistant"
echo "🤖 Agent Zero Multi-Agent Control"
echo "📊 Real-time System Monitoring"
echo "========================================"
echo ""

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[ℹ]${NC} $1"
}

print_step() {
    echo -e "${PURPLE}[🚀]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "integrated_sovereign_ai.py" ]; then
    echo "Looking for integrated_sovereign_ai.py..."
    if [ -f "/home/<USER>/project 1/integrated_sovereign_ai.py" ]; then
        cd "/home/<USER>/project 1"
        print_status "Found INTEGRATED SOVEREIGN-AI in project directory"
    else
        echo "❌ INTEGRATED SOVEREIGN-AI not found"
        exit 1
    fi
fi

print_step "Checking system requirements..."

# Check Python
if command -v python3 &> /dev/null; then
    print_status "Python 3 found"
else
    echo "❌ Python 3 not found. Please install Python 3."
    exit 1
fi

# Check QEMU
if command -v qemu-system-x86_64 &> /dev/null; then
    print_status "QEMU found"
else
    echo "⚠️ QEMU not found. REIGN neural core will not work without QEMU."
    echo "Install with: sudo apt install qemu-system-x86"
fi

# Check OVMF
if [ -f "/usr/share/ovmf/OVMF.fd" ]; then
    print_status "OVMF firmware found"
else
    echo "⚠️ OVMF firmware not found. Install with: sudo apt install ovmf"
fi

# Check for EFI files
EFI_FOUND=false
for efi in obsoletion_enhanced.efi obsoletion.efi test_working_efi.efi; do
    if [ -f "$efi" ]; then
        print_status "REIGN EFI found: $efi"
        EFI_FOUND=true
        break
    fi
done

if [ "$EFI_FOUND" = false ]; then
    echo "⚠️ No REIGN EFI files found. Neural core will need to be built."
fi

# Check Python packages
print_info "Checking Python packages..."
python3 -c "import tkinter, subprocess, threading, psutil" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️ Installing required Python packages..."
    pip3 install psutil
fi

echo ""
print_step "INTEGRATED SOVEREIGN-AI Launch Options:"
echo ""
echo "1. 🚀 Launch Full Integrated Interface"
echo "2. 🧠 REIGN Neural Core Test"
echo "3. 💬 SillyTavern Integration Test"
echo "4. 🔧 Component Check Only"
echo "5. ❌ Exit"
echo ""

read -p "Choose launch option (1-5): " choice

case $choice in
    1)
        print_step "Launching Full INTEGRATED SOVEREIGN-AI Interface..."
        echo ""
        print_info "🎮 Opening integrated control panel..."
        print_info "🧠 REIGN neural core ready for activation"
        print_info "💬 SillyTavern integration available"
        print_info "💻 Code Companion AI ready"
        print_info "🤖 Agent Zero multi-agent control ready"
        print_info "📊 System monitoring active"
        echo ""
        print_status "🎉 INTEGRATED SOVEREIGN-AI STARTING!"
        echo ""
        
        # Launch the integrated interface
        python3 integrated_sovereign_ai.py
        ;;
        
    2)
        print_step "Testing REIGN Neural Core..."
        echo ""
        print_info "This will test the REIGN neural core independently"
        print_info "Watch for Korean characters (꾯꾯꾯) - that's your AI thinking!"
        echo ""
        
        # Create minimal test environment
        mkdir -p test_reign
        
        # Find EFI file
        EFI_FILE=""
        for efi in obsoletion_enhanced.efi obsoletion.efi test_working_efi.efi; do
            if [ -f "$efi" ]; then
                EFI_FILE="$efi"
                break
            fi
        done
        
        if [ -n "$EFI_FILE" ]; then
            cp "$EFI_FILE" test_reign/obsoletion.efi
            
            cat > test_reign/startup.nsh << 'EOF'
cls
echo ======================================================================
echo                      REIGN NEURAL CORE TEST
echo                   INTEGRATED SOVEREIGN-AI Engine
echo ======================================================================
echo.
echo [REIGN] Testing neural network...
echo [REIGN] Initializing 50,000 parameters...
echo [REIGN] Starting inference engine...
echo.
obsoletion.efi
echo.
echo [REIGN] Test completed.
EOF
            
            print_info "Starting REIGN test..."
            qemu-system-x86_64 \
                -bios /usr/share/ovmf/OVMF.fd \
                -drive format=raw,file=fat:rw:test_reign \
                -m 2048 \
                -smp 2 \
                -boot order=c \
                -boot menu=off \
                -serial stdio \
                -monitor none \
                -nographic \
                -no-reboot
            
            rm -rf test_reign
        else
            echo "❌ No EFI file found for testing"
        fi
        ;;
        
    3)
        print_step "Testing SillyTavern Integration..."
        if [ -d "silly-tavern" ]; then
            print_info "Starting SillyTavern..."
            cd silly-tavern
            
            if [ ! -d "node_modules" ]; then
                print_info "Installing SillyTavern dependencies..."
                npm install
            fi
            
            print_status "SillyTavern starting on http://localhost:8000"
            node server.js
            cd ..
        else
            echo "❌ SillyTavern not found"
        fi
        ;;
        
    4)
        print_step "Component Check Complete"
        echo ""
        print_info "All checks completed. Use option 1 to launch the full interface."
        ;;
        
    5)
        print_info "Exiting INTEGRATED SOVEREIGN-AI launcher."
        exit 0
        ;;
        
    *)
        echo "❌ Invalid option. Please choose 1-5."
        exit 1
        ;;
esac

echo ""
print_step "INTEGRATED SOVEREIGN-AI Session Complete!"
print_status "🎉 Thank you for using the ultimate AI interface!"
print_info "Your AI empire awaits your return! 🚀"
