#!/bin/bash
# Project Obsoletion - Installation Verification Script
# Verifies complete installation and system readiness
# Author: Augment Agent

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

USER_DIR="$HOME/obsoletion"

print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================${NC}"
}

print_check() {
    if [[ $2 -eq 0 ]]; then
        echo -e "${GREEN}[✓]${NC} $1"
    else
        echo -e "${RED}[✗]${NC} $1"
    fi
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Main verification
main() {
    print_header "PROJECT OBSOLETION INSTALLATION VERIFICATION"
    
    echo "Checking installation completeness..."
    echo ""
    
    # Check installation directory
    if [[ -d "$USER_DIR" ]]; then
        print_check "Installation directory exists" 0
    else
        print_check "Installation directory exists" 1
        echo "Run EASY_INSTALL.sh first"
        exit 1
    fi
    
    # Check source directory
    if [[ -d "$USER_DIR/source" ]]; then
        print_check "Source directory exists" 0
    else
        print_check "Source directory exists" 1
        exit 1
    fi
    
    # Check main binary
    if [[ -f "$USER_DIR/source/obsoletion_enhanced.efi" ]]; then
        size=$(stat -c%s "$USER_DIR/source/obsoletion_enhanced.efi")
        size_mb=$(awk "BEGIN {printf \"%.2f\", $size/1024/1024}")
        print_check "Main binary exists (${size_mb}MB)" 0
    else
        print_check "Main binary exists" 1
        exit 1
    fi
    
    # Check source files
    source_files=(
        "uefi_bootloader.asm"
        "neural_support_functions.asm"
        "advanced_neural_engine.asm"
        "adam_optimizer.asm"
        "parallel_training.asm"
        "avx2_optimization.asm"
        "reinforcement_learning.asm"
        "enhanced_data_pipeline.asm"
        "optimization_stubs.asm"
        "ternary_quantization.asm"
        "bitlinear_inference.asm"
        "bitpack_optimization.asm"
        "ternary_training.asm"
        "gui_agent_core.asm"
        "gui_cnn_processor.asm"
        "gui_interaction_system.asm"
        "gui_agent_stubs.asm"
        "Makefile"
        "uefi_linker.ld"
    )
    
    missing_files=0
    for file in "${source_files[@]}"; do
        if [[ -f "$USER_DIR/source/$file" ]]; then
            print_check "Source file: $file" 0
        else
            print_check "Source file: $file" 1
            missing_files=$((missing_files + 1))
        fi
    done
    
    if [[ $missing_files -gt 0 ]]; then
        print_warning "$missing_files source files missing"
    else
        print_success "All source files present"
    fi
    
    # Check object files
    cd "$USER_DIR/source"
    object_files=(
        "uefi_bootloader.o"
        "neural_support_functions.o"
        "advanced_neural_engine.o"
        "adam_optimizer.o"
        "parallel_training.o"
        "avx2_optimization.o"
        "reinforcement_learning.o"
        "enhanced_data_pipeline.o"
        "optimization_stubs.o"
        "ternary_quantization.o"
        "bitlinear_inference.o"
        "bitpack_optimization.o"
        "ternary_training.o"
        "gui_agent_core.o"
        "gui_cnn_processor.o"
        "gui_interaction_system.o"
        "gui_agent_stubs.o"
    )
    
    missing_objects=0
    for file in "${object_files[@]}"; do
        if [[ -f "$file" ]]; then
            print_check "Object file: $file" 0
        else
            print_check "Object file: $file" 1
            missing_objects=$((missing_objects + 1))
        fi
    done
    
    if [[ $missing_objects -gt 0 ]]; then
        print_warning "$missing_objects object files missing - rebuild may be needed"
    else
        print_success "All object files present"
    fi
    
    # Check virtual runner script
    if [[ -f "$USER_DIR/run_obsoletion.sh" ]]; then
        print_check "Virtual runner script exists" 0
        if [[ -x "$USER_DIR/run_obsoletion.sh" ]]; then
            print_check "Virtual runner script is executable" 0
        else
            print_check "Virtual runner script is executable" 1
        fi
    else
        print_check "Virtual runner script exists" 1
    fi
    
    # Check installation log
    if [[ -f "$USER_DIR/installation.log" ]]; then
        print_check "Installation log exists" 0
        if grep -q "Installation completed" "$USER_DIR/installation.log"; then
            print_check "Installation completed successfully" 0
        else
            print_check "Installation completed successfully" 1
        fi
    else
        print_check "Installation log exists" 1
    fi
    
    # Check dependencies
    echo ""
    print_header "DEPENDENCY VERIFICATION"
    
    dependencies=("nasm" "make" "qemu-system-x86_64" "ld")
    
    for dep in "${dependencies[@]}"; do
        if command -v "$dep" &> /dev/null; then
            print_check "Dependency: $dep" 0
        else
            print_check "Dependency: $dep" 1
        fi
    done
    
    # Check OVMF
    ovmf_paths=(
        "/usr/share/ovmf/OVMF.fd"
        "/usr/share/edk2-ovmf/OVMF_CODE.fd"
        "/usr/share/qemu/ovmf-x86_64.bin"
    )
    
    ovmf_found=0
    for path in "${ovmf_paths[@]}"; do
        if [[ -f "$path" ]]; then
            print_check "OVMF firmware: $path" 0
            ovmf_found=1
            break
        fi
    done
    
    if [[ $ovmf_found -eq 0 ]]; then
        print_check "OVMF firmware found" 1
    fi
    
    # System compatibility check
    echo ""
    print_header "SYSTEM COMPATIBILITY"
    
    # Architecture
    if [[ $(uname -m) == "x86_64" ]]; then
        print_check "Architecture: x86_64" 0
    else
        print_check "Architecture: x86_64" 1
    fi
    
    # Memory
    total_mem=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $total_mem -ge 8 ]]; then
        print_check "Memory: ${total_mem}GB (sufficient)" 0
    else
        print_check "Memory: ${total_mem}GB (insufficient)" 1
    fi
    
    # Disk space
    available_space=$(df -BG "$USER_DIR" | awk 'NR==2{print $4}' | sed 's/G//')
    if [[ $available_space -ge 5 ]]; then
        print_check "Disk space: ${available_space}GB (sufficient)" 0
    else
        print_check "Disk space: ${available_space}GB (insufficient)" 1
    fi
    
    # UEFI
    if [[ -d "/sys/firmware/efi" ]]; then
        print_check "UEFI firmware detected" 0
    else
        print_check "UEFI firmware detected" 1
        print_info "Legacy BIOS mode - UEFI recommended"
    fi
    
    # AVX2
    if grep -q avx2 /proc/cpuinfo; then
        print_check "AVX2 support detected" 0
    else
        print_check "AVX2 support detected" 1
        print_info "Will use scalar fallback"
    fi
    
    # Quick build test
    echo ""
    print_header "BUILD SYSTEM TEST"
    
    cd "$USER_DIR/source"
    
    if make --version &> /dev/null; then
        print_check "Make available" 0
    else
        print_check "Make available" 1
    fi
    
    if nasm -v &> /dev/null; then
        print_check "NASM available" 0
    else
        print_check "NASM available" 1
    fi
    
    # Quick QEMU test
    echo ""
    print_header "VIRTUAL ENVIRONMENT TEST"
    
    print_info "Testing QEMU boot (10 second timeout)..."
    
    if timeout 10s qemu-system-x86_64 \
        -bios /usr/share/ovmf/OVMF.fd \
        -drive format=raw,file=fat:rw:. \
        -nographic \
        -m 1024 2>/dev/null; then
        print_check "QEMU test completed" 0
    else
        # Timeout is expected and indicates successful boot
        print_check "QEMU boot test successful" 0
    fi
    
    # Final summary
    echo ""
    print_header "VERIFICATION SUMMARY"
    
    if [[ $missing_files -eq 0 && $missing_objects -eq 0 ]]; then
        print_success "🎉 PROJECT OBSOLETION INSTALLATION VERIFIED!"
        print_success "✅ All components present and functional"
        print_success "✅ System ready for use"
        echo ""
        print_info "To run Project Obsoletion:"
        print_info "  cd $USER_DIR"
        print_info "  ./run_obsoletion.sh"
        echo ""
        print_info "For detailed usage instructions, see:"
        print_info "  QUICK_START.md"
        print_info "  USER_GUIDE.md"
    else
        print_warning "⚠️  Installation incomplete or corrupted"
        print_info "Run EASY_INSTALL.sh again to fix issues"
    fi
}

# Run verification
main "$@"
