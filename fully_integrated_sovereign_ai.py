#!/usr/bin/env python3
"""
FULLY INTEGRATED SOVEREIGN-AI
Complete integration of ALL features from Agent Zero, Code Companion, and SillyTavern
with REIGN neural core - no half measures, everything included!
"""

import os
import sys
import json
import asyncio
import threading
import subprocess
import time
import webbrowser
import tempfile
import shutil
from pathlib import Path
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import websocket
import requests
from flask import Flask, request, jsonify, render_template_string, send_from_directory
import socketio
from werkzeug.serving import make_server
import psutil
import sqlite3
import uuid
from datetime import datetime, timezone
import yaml
import markdown
import re
import mimetypes

class FullyIntegratedSovereignAI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("FULLY INTEGRATED SOVEREIGN-AI: Complete AI Empire")
        self.root.geometry("1920x1080")
        self.root.configure(bg='#0a0a0a')
        
        # Core processes
        self.reign_process = None
        self.flask_server = None
        self.server_thread = None
        
        # Agent Zero Integration
        self.agent_zero_core = None
        self.active_agents = {}
        self.agent_tasks = []
        
        # SillyTavern Integration
        self.characters = {}
        self.current_chat = None
        self.chat_history = []
        self.extensions = {}
        
        # Code Companion Integration
        self.code_sessions = {}
        self.active_files = {}
        self.language_servers = {}
        
        # Browser Integration
        self.browser_sessions = {}
        self.web_content_cache = {}
        
        # Database for persistence
        self.db_path = "sovereign_ai.db"
        self.init_database()
        
        # Flask app for unified API
        self.app = Flask(__name__)
        self.setup_flask_routes()
        
        self.setup_ui()
        self.load_all_components()
    
    def init_database(self):
        """Initialize SQLite database for all components."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Agent Zero tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS agents (
                id TEXT PRIMARY KEY,
                name TEXT,
                type TEXT,
                config TEXT,
                status TEXT,
                created_at TIMESTAMP
            )
        ''')
        
        # SillyTavern tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS characters (
                id TEXT PRIMARY KEY,
                name TEXT,
                description TEXT,
                personality TEXT,
                avatar TEXT,
                data TEXT,
                created_at TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS chats (
                id TEXT PRIMARY KEY,
                character_id TEXT,
                messages TEXT,
                metadata TEXT,
                created_at TIMESTAMP
            )
        ''')
        
        # Code Companion tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS code_sessions (
                id TEXT PRIMARY KEY,
                name TEXT,
                files TEXT,
                language TEXT,
                workspace TEXT,
                created_at TIMESTAMP
            )
        ''')
        
        # Browser sessions
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS browser_sessions (
                id TEXT PRIMARY KEY,
                url TEXT,
                content TEXT,
                metadata TEXT,
                created_at TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def setup_flask_routes(self):
        """Setup all Flask routes for unified API."""
        
        # Agent Zero API routes
        @self.app.route('/api/agents', methods=['GET', 'POST'])
        def agents_api():
            if request.method == 'GET':
                return jsonify(list(self.active_agents.values()))
            elif request.method == 'POST':
                return self.create_agent(request.json)
        
        @self.app.route('/api/agents/<agent_id>/execute', methods=['POST'])
        def execute_agent_task(agent_id):
            return self.execute_agent_command(agent_id, request.json)
        
        # SillyTavern API routes
        @self.app.route('/api/characters', methods=['GET', 'POST'])
        def characters_api():
            if request.method == 'GET':
                return jsonify(list(self.characters.values()))
            elif request.method == 'POST':
                return self.create_character(request.json)
        
        @self.app.route('/api/chat/<character_id>', methods=['POST'])
        def chat_with_character(character_id):
            return self.process_chat_message(character_id, request.json)
        
        # Code Companion API routes
        @self.app.route('/api/code/complete', methods=['POST'])
        def code_completion():
            return self.complete_code(request.json)
        
        @self.app.route('/api/code/analyze', methods=['POST'])
        def code_analysis():
            return self.analyze_code(request.json)
        
        @self.app.route('/api/code/refactor', methods=['POST'])
        def code_refactor():
            return self.refactor_code(request.json)
        
        # Browser API routes
        @self.app.route('/api/browser/navigate', methods=['POST'])
        def browser_navigate():
            return self.navigate_browser(request.json)
        
        @self.app.route('/api/browser/interact', methods=['POST'])
        def browser_interact():
            return self.interact_with_page(request.json)
        
        # REIGN neural core API
        @self.app.route('/api/reign/query', methods=['POST'])
        def reign_query():
            return self.query_reign_neural_core(request.json)
        
        # Unified interface
        @self.app.route('/')
        def unified_interface():
            return self.render_unified_interface()
        
        # Static files
        @self.app.route('/static/<path:filename>')
        def static_files(filename):
            return send_from_directory('static', filename)
    
    def setup_ui(self):
        """Setup the complete integrated UI."""
        # Create notebook with all integrated features
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TNotebook.Tab', padding=[15, 8])
        
        # Tab 1: REIGN Control Center
        self.setup_reign_tab()
        
        # Tab 2: Agent Zero Multi-Agent System
        self.setup_agent_zero_tab()
        
        # Tab 3: SillyTavern Chat & Roleplay
        self.setup_silly_tavern_tab()
        
        # Tab 4: Code Companion IDE
        self.setup_code_companion_tab()
        
        # Tab 5: Browser Integration
        self.setup_browser_tab()
        
        # Tab 6: System Monitor
        self.setup_monitor_tab()
        
        # Status bar
        self.setup_status_bar()
    
    def setup_reign_tab(self):
        """Setup REIGN neural core control tab."""
        self.reign_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(self.reign_frame, text='🧠 REIGN Neural Core')
        
        # Title
        title_label = tk.Label(self.reign_frame,
                              text="REIGN NEURAL CORE CONTROL CENTER",
                              font=('Courier', 20, 'bold'),
                              fg='#ff6600', bg='#1a1a1a')
        title_label.pack(pady=10)
        
        # Main container
        main_container = tk.Frame(self.reign_frame, bg='#1a1a1a')
        main_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Left panel - Configuration and controls
        config_frame = tk.LabelFrame(main_container, text="Neural Core Configuration",
                                   font=('Courier', 14, 'bold'),
                                   fg='#00ff00', bg='#1a1a1a')
        config_frame.pack(side='left', fill='y', padx=(0, 10))
        
        # EFI Configuration
        tk.Label(config_frame, text="REIGN EFI File:", 
                font=('Courier', 12), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=5)
        
        self.efi_var = tk.StringVar(value="obsoletion_enhanced.efi")
        efi_frame = tk.Frame(config_frame, bg='#1a1a1a')
        efi_frame.pack(fill='x', pady=5)
        
        efi_entry = tk.Entry(efi_frame, textvariable=self.efi_var,
                           bg='#2a2a2a', fg='#ffffff', font=('Courier', 10))
        efi_entry.pack(side='left', fill='x', expand=True)
        
        tk.Button(efi_frame, text="Browse", command=self.browse_efi_file,
                 bg='#333333', fg='#ffffff').pack(side='right', padx=(5, 0))
        
        # Neural Network Settings
        tk.Label(config_frame, text="Memory (MB):", 
                font=('Courier', 12), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=5)
        
        self.memory_var = tk.StringVar(value="4096")
        memory_entry = tk.Entry(config_frame, textvariable=self.memory_var,
                              bg='#2a2a2a', fg='#ffffff', font=('Courier', 10))
        memory_entry.pack(fill='x', pady=5)
        
        tk.Label(config_frame, text="CPU Cores:", 
                font=('Courier', 12), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=5)
        
        self.cpu_var = tk.StringVar(value="4")
        cpu_entry = tk.Entry(config_frame, textvariable=self.cpu_var,
                           bg='#2a2a2a', fg='#ffffff', font=('Courier', 10))
        cpu_entry.pack(fill='x', pady=5)
        
        # Neural Network Parameters
        tk.Label(config_frame, text="Neural Parameters:", 
                font=('Courier', 12), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=5)
        
        self.params_var = tk.StringVar(value="50000")
        params_entry = tk.Entry(config_frame, textvariable=self.params_var,
                              bg='#2a2a2a', fg='#ffffff', font=('Courier', 10))
        params_entry.pack(fill='x', pady=5)
        
        # Control buttons
        button_frame = tk.Frame(config_frame, bg='#1a1a1a')
        button_frame.pack(fill='x', pady=20)
        
        self.start_reign_btn = tk.Button(button_frame, text="🚀 START REIGN CORE",
                                       command=self.start_reign_neural_core,
                                       bg='#ff6600', fg='#ffffff',
                                       font=('Arial', 14, 'bold'))
        self.start_reign_btn.pack(fill='x', pady=5)
        
        self.stop_reign_btn = tk.Button(button_frame, text="⏹️ STOP REIGN CORE",
                                      command=self.stop_reign_neural_core,
                                      bg='#666666', fg='#ffffff',
                                      font=('Arial', 14, 'bold'),
                                      state='disabled')
        self.stop_reign_btn.pack(fill='x', pady=5)
        
        tk.Button(button_frame, text="🧠 Neural Network Test",
                 command=self.test_neural_network,
                 bg='#0066aa', fg='#ffffff',
                 font=('Arial', 12, 'bold')).pack(fill='x', pady=5)
        
        tk.Button(button_frame, text="🔧 Build Enhanced EFI",
                 command=self.build_enhanced_efi,
                 bg='#006600', fg='#ffffff',
                 font=('Arial', 12, 'bold')).pack(fill='x', pady=5)
        
        # Right panel - Neural output and monitoring
        output_frame = tk.LabelFrame(main_container, text="Neural Network Output & Monitoring",
                                   font=('Courier', 14, 'bold'),
                                   fg='#00ff00', bg='#1a1a1a')
        output_frame.pack(side='right', fill='both', expand=True)
        
        # Neural output display
        self.reign_output = scrolledtext.ScrolledText(output_frame,
                                                    bg='#000000', fg='#00ff00',
                                                    font=('Courier', 10),
                                                    wrap='word')
        self.reign_output.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Neural activity indicators
        indicators_frame = tk.Frame(output_frame, bg='#1a1a1a')
        indicators_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Label(indicators_frame, text="Neural Activity:", 
                font=('Courier', 10, 'bold'), fg='#ffffff', bg='#1a1a1a').pack(side='left')
        
        self.neural_activity_label = tk.Label(indicators_frame,
                                            text="Offline",
                                            font=('Courier', 10),
                                            fg='#ff0000', bg='#1a1a1a')
        self.neural_activity_label.pack(side='left', padx=10)
        
        tk.Label(indicators_frame, text="Inference Speed:", 
                font=('Courier', 10, 'bold'), fg='#ffffff', bg='#1a1a1a').pack(side='left', padx=20)
        
        self.inference_speed_label = tk.Label(indicators_frame,
                                            text="0.00ms",
                                            font=('Courier', 10),
                                            fg='#ffff00', bg='#1a1a1a')
        self.inference_speed_label.pack(side='left', padx=10)
        
        # Quick actions
        quick_frame = tk.Frame(output_frame, bg='#1a1a1a')
        quick_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Button(quick_frame, text="Clear Output", command=self.clear_reign_output,
                 bg='#333333', fg='#ffffff').pack(side='left', padx=5)
        
        tk.Button(quick_frame, text="Save Neural Log", command=self.save_neural_log,
                 bg='#333333', fg='#ffffff').pack(side='left', padx=5)
        
        tk.Button(quick_frame, text="Export Neural State", command=self.export_neural_state,
                 bg='#006600', fg='#ffffff').pack(side='right', padx=5)
    
    def setup_agent_zero_tab(self):
        """Setup complete Agent Zero multi-agent system tab."""
        self.agent_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(self.agent_frame, text='🤖 Agent Zero Multi-Agent')
        
        # Title
        title_label = tk.Label(self.agent_frame,
                              text="AGENT ZERO MULTI-AGENT SYSTEM",
                              font=('Courier', 20, 'bold'),
                              fg='#00aa00', bg='#1a1a1a')
        title_label.pack(pady=10)
        
        # Main container
        main_container = tk.Frame(self.agent_frame, bg='#1a1a1a')
        main_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Left panel - Agent management
        agent_mgmt_frame = tk.LabelFrame(main_container, text="Agent Management",
                                       font=('Courier', 14, 'bold'),
                                       fg='#00aa00', bg='#1a1a1a')
        agent_mgmt_frame.pack(side='left', fill='y', padx=(0, 10))
        
        # Agent creation
        tk.Label(agent_mgmt_frame, text="Create New Agent:", 
                font=('Courier', 12, 'bold'), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=5)
        
        self.agent_name_var = tk.StringVar()
        tk.Entry(agent_mgmt_frame, textvariable=self.agent_name_var,
                bg='#2a2a2a', fg='#ffffff', font=('Courier', 10)).pack(fill='x', pady=2)
        
        self.agent_type_var = tk.StringVar(value="general")
        agent_type_combo = ttk.Combobox(agent_mgmt_frame, textvariable=self.agent_type_var,
                                      values=["general", "hacker", "coder", "browser", "system", "security"])
        agent_type_combo.pack(fill='x', pady=2)
        
        tk.Button(agent_mgmt_frame, text="🤖 Create Agent",
                 command=self.create_new_agent,
                 bg='#00aa00', fg='#ffffff',
                 font=('Arial', 10, 'bold')).pack(fill='x', pady=5)
        
        # Active agents list
        tk.Label(agent_mgmt_frame, text="Active Agents:", 
                font=('Courier', 12, 'bold'), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=(20, 5))
        
        self.agents_listbox = tk.Listbox(agent_mgmt_frame,
                                       bg='#2a2a2a', fg='#ffffff',
                                       font=('Courier', 9),
                                       height=8)
        self.agents_listbox.pack(fill='x', pady=5)
        
        # Agent control buttons
        agent_control_frame = tk.Frame(agent_mgmt_frame, bg='#1a1a1a')
        agent_control_frame.pack(fill='x', pady=5)
        
        tk.Button(agent_control_frame, text="Deploy",
                 command=self.deploy_selected_agent,
                 bg='#006600', fg='#ffffff').pack(side='left', padx=2)
        
        tk.Button(agent_control_frame, text="Pause",
                 command=self.pause_selected_agent,
                 bg='#666600', fg='#ffffff').pack(side='left', padx=2)
        
        tk.Button(agent_control_frame, text="Terminate",
                 command=self.terminate_selected_agent,
                 bg='#aa0000', fg='#ffffff').pack(side='left', padx=2)
        
        # System command interface
        cmd_frame = tk.LabelFrame(agent_mgmt_frame, text="System Commands",
                                font=('Courier', 12, 'bold'),
                                fg='#ffff00', bg='#1a1a1a')
        cmd_frame.pack(fill='x', pady=(20, 0))
        
        self.system_command_var = tk.StringVar()
        cmd_entry = tk.Entry(cmd_frame, textvariable=self.system_command_var,
                           bg='#000000', fg='#00ff00',
                           font=('Courier', 10))
        cmd_entry.pack(fill='x', padx=5, pady=5)
        cmd_entry.bind('<Return>', lambda e: self.execute_system_command())
        
        cmd_buttons = tk.Frame(cmd_frame, bg='#1a1a1a')
        cmd_buttons.pack(fill='x', padx=5, pady=5)
        
        tk.Button(cmd_buttons, text="Execute",
                 command=self.execute_system_command,
                 bg='#00aa00', fg='#ffffff').pack(side='left', padx=2)
        
        tk.Button(cmd_buttons, text="Scan System",
                 command=self.perform_system_scan,
                 bg='#0066aa', fg='#ffffff').pack(side='left', padx=2)
        
        tk.Button(cmd_buttons, text="Security Audit",
                 command=self.perform_security_audit,
                 bg='#aa6600', fg='#ffffff').pack(side='left', padx=2)
        
        # Right panel - Agent output and tasks
        output_frame = tk.LabelFrame(main_container, text="Agent Output & Task Management",
                                   font=('Courier', 14, 'bold'),
                                   fg='#00aa00', bg='#1a1a1a')
        output_frame.pack(side='right', fill='both', expand=True)
        
        # Task management
        task_mgmt_frame = tk.Frame(output_frame, bg='#1a1a1a')
        task_mgmt_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Label(task_mgmt_frame, text="Task Assignment:", 
                font=('Courier', 12, 'bold'), fg='#ffffff', bg='#1a1a1a').pack(side='left')
        
        self.task_input_var = tk.StringVar()
        task_entry = tk.Entry(task_mgmt_frame, textvariable=self.task_input_var,
                            bg='#2a2a2a', fg='#ffffff', font=('Courier', 10))
        task_entry.pack(side='left', fill='x', expand=True, padx=5)
        
        tk.Button(task_mgmt_frame, text="Assign Task",
                 command=self.assign_task_to_agent,
                 bg='#0066aa', fg='#ffffff').pack(side='right')
        
        # Agent output display
        self.agent_output = scrolledtext.ScrolledText(output_frame,
                                                    bg='#000000', fg='#00ff00',
                                                    font=('Courier', 10),
                                                    wrap='word')
        self.agent_output.pack(fill='both', expand=True, padx=5, pady=5)

    def setup_silly_tavern_tab(self):
        """Setup complete SillyTavern chat and roleplay system."""
        self.tavern_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(self.tavern_frame, text='💬 SillyTavern Chat & Roleplay')

        # Title
        title_label = tk.Label(self.tavern_frame,
                              text="SILLYTAVERN CHAT & ROLEPLAY SYSTEM",
                              font=('Courier', 20, 'bold'),
                              fg='#ff00ff', bg='#1a1a1a')
        title_label.pack(pady=10)

        # Main container
        main_container = tk.Frame(self.tavern_frame, bg='#1a1a1a')
        main_container.pack(fill='both', expand=True, padx=10, pady=10)

        # Left panel - Character management
        char_mgmt_frame = tk.LabelFrame(main_container, text="Character Management",
                                      font=('Courier', 14, 'bold'),
                                      fg='#ff00ff', bg='#1a1a1a')
        char_mgmt_frame.pack(side='left', fill='y', padx=(0, 10))

        # Character creation
        tk.Label(char_mgmt_frame, text="Create Character:",
                font=('Courier', 12, 'bold'), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=5)

        self.char_name_var = tk.StringVar()
        tk.Entry(char_mgmt_frame, textvariable=self.char_name_var,
                bg='#2a2a2a', fg='#ffffff', font=('Courier', 10)).pack(fill='x', pady=2)

        tk.Label(char_mgmt_frame, text="Personality:",
                font=('Courier', 10), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=(10, 2))

        self.char_personality = scrolledtext.ScrolledText(char_mgmt_frame,
                                                        bg='#2a2a2a', fg='#ffffff',
                                                        font=('Courier', 9),
                                                        height=4)
        self.char_personality.pack(fill='x', pady=2)

        tk.Label(char_mgmt_frame, text="Scenario:",
                font=('Courier', 10), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=(10, 2))

        self.char_scenario = scrolledtext.ScrolledText(char_mgmt_frame,
                                                     bg='#2a2a2a', fg='#ffffff',
                                                     font=('Courier', 9),
                                                     height=3)
        self.char_scenario.pack(fill='x', pady=2)

        tk.Button(char_mgmt_frame, text="💬 Create Character",
                 command=self.create_new_character,
                 bg='#ff00ff', fg='#ffffff',
                 font=('Arial', 10, 'bold')).pack(fill='x', pady=5)

        # Character list
        tk.Label(char_mgmt_frame, text="Characters:",
                font=('Courier', 12, 'bold'), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=(20, 5))

        self.characters_listbox = tk.Listbox(char_mgmt_frame,
                                           bg='#2a2a2a', fg='#ffffff',
                                           font=('Courier', 9),
                                           height=6)
        self.characters_listbox.pack(fill='x', pady=5)
        self.characters_listbox.bind('<Double-Button-1>', self.load_character)

        # Character controls
        char_control_frame = tk.Frame(char_mgmt_frame, bg='#1a1a1a')
        char_control_frame.pack(fill='x', pady=5)

        tk.Button(char_control_frame, text="Load",
                 command=self.load_selected_character,
                 bg='#006600', fg='#ffffff').pack(side='left', padx=2)

        tk.Button(char_control_frame, text="Edit",
                 command=self.edit_selected_character,
                 bg='#666600', fg='#ffffff').pack(side='left', padx=2)

        tk.Button(char_control_frame, text="Delete",
                 command=self.delete_selected_character,
                 bg='#aa0000', fg='#ffffff').pack(side='left', padx=2)

        # Extensions and features
        ext_frame = tk.LabelFrame(char_mgmt_frame, text="Extensions & Features",
                                font=('Courier', 12, 'bold'),
                                fg='#ffff00', bg='#1a1a1a')
        ext_frame.pack(fill='x', pady=(20, 0))

        self.voice_chat_var = tk.BooleanVar()
        tk.Checkbutton(ext_frame, text="Voice Chat",
                      variable=self.voice_chat_var,
                      font=('Courier', 10), fg='#ffffff', bg='#1a1a1a',
                      selectcolor='#333333').pack(anchor='w', pady=2)

        self.image_gen_var = tk.BooleanVar()
        tk.Checkbutton(ext_frame, text="Image Generation",
                      variable=self.image_gen_var,
                      font=('Courier', 10), fg='#ffffff', bg='#1a1a1a',
                      selectcolor='#333333').pack(anchor='w', pady=2)

        self.world_info_var = tk.BooleanVar()
        tk.Checkbutton(ext_frame, text="World Info",
                      variable=self.world_info_var,
                      font=('Courier', 10), fg='#ffffff', bg='#1a1a1a',
                      selectcolor='#333333').pack(anchor='w', pady=2)

        tk.Button(ext_frame, text="Import from Chub",
                 command=self.import_from_chub,
                 bg='#0066aa', fg='#ffffff').pack(fill='x', pady=5)

        # Right panel - Chat interface
        chat_frame = tk.LabelFrame(main_container, text="Chat Interface",
                                 font=('Courier', 14, 'bold'),
                                 fg='#ff00ff', bg='#1a1a1a')
        chat_frame.pack(side='right', fill='both', expand=True)

        # Current character display
        current_char_frame = tk.Frame(chat_frame, bg='#1a1a1a')
        current_char_frame.pack(fill='x', padx=5, pady=5)

        tk.Label(current_char_frame, text="Current Character:",
                font=('Courier', 12, 'bold'), fg='#ffffff', bg='#1a1a1a').pack(side='left')

        self.current_char_label = tk.Label(current_char_frame,
                                         text="None",
                                         font=('Courier', 12),
                                         fg='#ff00ff', bg='#1a1a1a')
        self.current_char_label.pack(side='left', padx=10)

        # Chat display
        self.chat_display = scrolledtext.ScrolledText(chat_frame,
                                                    bg='#000000', fg='#ffffff',
                                                    font=('Courier', 11),
                                                    wrap='word')
        self.chat_display.pack(fill='both', expand=True, padx=5, pady=5)

        # Message input
        input_frame = tk.Frame(chat_frame, bg='#1a1a1a')
        input_frame.pack(fill='x', padx=5, pady=5)

        self.message_input = scrolledtext.ScrolledText(input_frame,
                                                     bg='#2a2a2a', fg='#ffffff',
                                                     font=('Courier', 11),
                                                     height=3)
        self.message_input.pack(fill='x', pady=2)
        self.message_input.bind('<Control-Return>', lambda e: self.send_chat_message())

        # Chat controls
        chat_control_frame = tk.Frame(input_frame, bg='#1a1a1a')
        chat_control_frame.pack(fill='x', pady=5)

        tk.Button(chat_control_frame, text="💬 Send Message",
                 command=self.send_chat_message,
                 bg='#ff00ff', fg='#ffffff',
                 font=('Arial', 12, 'bold')).pack(side='left', padx=5)

        tk.Button(chat_control_frame, text="🎭 Regenerate",
                 command=self.regenerate_response,
                 bg='#6600aa', fg='#ffffff').pack(side='left', padx=5)

        tk.Button(chat_control_frame, text="💾 Save Chat",
                 command=self.save_current_chat,
                 bg='#006600', fg='#ffffff').pack(side='right', padx=5)

        tk.Button(chat_control_frame, text="📂 Load Chat",
                 command=self.load_chat_history,
                 bg='#0066aa', fg='#ffffff').pack(side='right', padx=5)

    def setup_code_companion_tab(self):
        """Setup complete Code Companion IDE system."""
        self.code_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(self.code_frame, text='💻 Code Companion IDE')

        # Title
        title_label = tk.Label(self.code_frame,
                              text="CODE COMPANION INTEGRATED DEVELOPMENT ENVIRONMENT",
                              font=('Courier', 20, 'bold'),
                              fg='#0066ff', bg='#1a1a1a')
        title_label.pack(pady=10)

        # Main container
        main_container = tk.Frame(self.code_frame, bg='#1a1a1a')
        main_container.pack(fill='both', expand=True, padx=10, pady=10)

        # Left panel - File explorer and project management
        project_frame = tk.LabelFrame(main_container, text="Project Explorer",
                                    font=('Courier', 14, 'bold'),
                                    fg='#0066ff', bg='#1a1a1a')
        project_frame.pack(side='left', fill='y', padx=(0, 10))

        # Project controls
        project_control_frame = tk.Frame(project_frame, bg='#1a1a1a')
        project_control_frame.pack(fill='x', pady=5)

        tk.Button(project_control_frame, text="📁 Open Project",
                 command=self.open_project,
                 bg='#0066aa', fg='#ffffff').pack(side='left', padx=2)

        tk.Button(project_control_frame, text="📄 New File",
                 command=self.create_new_file,
                 bg='#006600', fg='#ffffff').pack(side='left', padx=2)

        # File tree
        self.file_tree = ttk.Treeview(project_frame, height=15)
        self.file_tree.pack(fill='both', expand=True, pady=5)
        self.file_tree.bind('<Double-Button-1>', self.open_file_from_tree)

        # Language and tools
        tools_frame = tk.LabelFrame(project_frame, text="Language Tools",
                                  font=('Courier', 12, 'bold'),
                                  fg='#ffff00', bg='#1a1a1a')
        tools_frame.pack(fill='x', pady=(20, 0))

        tk.Label(tools_frame, text="Language:",
                font=('Courier', 10), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=2)

        self.language_var = tk.StringVar(value="python")
        language_combo = ttk.Combobox(tools_frame, textvariable=self.language_var,
                                    values=["python", "javascript", "typescript", "java", "c++", "rust", "go", "lua"])
        language_combo.pack(fill='x', pady=2)

        tk.Button(tools_frame, text="🔧 Start Language Server",
                 command=self.start_language_server,
                 bg='#666600', fg='#ffffff').pack(fill='x', pady=5)

        # Right panel - Code editor and AI assistance
        editor_frame = tk.LabelFrame(main_container, text="Code Editor & AI Assistant",
                                   font=('Courier', 14, 'bold'),
                                   fg='#0066ff', bg='#1a1a1a')
        editor_frame.pack(side='right', fill='both', expand=True)

        # File tabs
        self.file_tabs = ttk.Notebook(editor_frame)
        self.file_tabs.pack(fill='both', expand=True, padx=5, pady=5)

        # AI assistance panel
        ai_frame = tk.Frame(editor_frame, bg='#1a1a1a')
        ai_frame.pack(fill='x', padx=5, pady=5)

        # AI prompt input
        tk.Label(ai_frame, text="AI Assistant:",
                font=('Courier', 12, 'bold'), fg='#ffffff', bg='#1a1a1a').pack(anchor='w')

        self.ai_prompt_var = tk.StringVar()
        ai_entry = tk.Entry(ai_frame, textvariable=self.ai_prompt_var,
                          bg='#2a2a2a', fg='#ffffff', font=('Courier', 11))
        ai_entry.pack(fill='x', pady=2)
        ai_entry.bind('<Return>', lambda e: self.process_ai_request())

        # AI action buttons
        ai_buttons_frame = tk.Frame(ai_frame, bg='#1a1a1a')
        ai_buttons_frame.pack(fill='x', pady=5)

        tk.Button(ai_buttons_frame, text="💡 Complete Code",
                 command=self.ai_complete_code,
                 bg='#0066aa', fg='#ffffff').pack(side='left', padx=2)

        tk.Button(ai_buttons_frame, text="📖 Explain Code",
                 command=self.ai_explain_code,
                 bg='#006600', fg='#ffffff').pack(side='left', padx=2)

        tk.Button(ai_buttons_frame, text="🔧 Refactor Code",
                 command=self.ai_refactor_code,
                 bg='#aa6600', fg='#ffffff').pack(side='left', padx=2)

        tk.Button(ai_buttons_frame, text="🐛 Debug Code",
                 command=self.ai_debug_code,
                 bg='#aa0066', fg='#ffffff').pack(side='left', padx=2)

        tk.Button(ai_buttons_frame, text="🧪 Generate Tests",
                 command=self.ai_generate_tests,
                 bg='#6600aa', fg='#ffffff').pack(side='left', padx=2)

        # Terminal integration
        terminal_frame = tk.LabelFrame(editor_frame, text="Integrated Terminal",
                                     font=('Courier', 12, 'bold'),
                                     fg='#00ff00', bg='#1a1a1a')
        terminal_frame.pack(fill='x', padx=5, pady=5)

        self.terminal_output = scrolledtext.ScrolledText(terminal_frame,
                                                       bg='#000000', fg='#00ff00',
                                                       font=('Courier', 10),
                                                       height=8)
        self.terminal_output.pack(fill='x', padx=5, pady=5)

        terminal_input_frame = tk.Frame(terminal_frame, bg='#1a1a1a')
        terminal_input_frame.pack(fill='x', padx=5, pady=5)

        tk.Label(terminal_input_frame, text="$",
                font=('Courier', 12, 'bold'), fg='#00ff00', bg='#1a1a1a').pack(side='left')

        self.terminal_command_var = tk.StringVar()
        terminal_entry = tk.Entry(terminal_input_frame, textvariable=self.terminal_command_var,
                                bg='#000000', fg='#00ff00', font=('Courier', 11))
        terminal_entry.pack(side='left', fill='x', expand=True, padx=5)
        terminal_entry.bind('<Return>', lambda e: self.execute_terminal_command())

        tk.Button(terminal_input_frame, text="Execute",
                 command=self.execute_terminal_command,
                 bg='#006600', fg='#ffffff').pack(side='right')
