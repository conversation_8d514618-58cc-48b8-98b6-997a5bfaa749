/* Project Obsoletion - UEFI Linker Script
 * Author: Augment Agent
 * Description: Linker script for UEFI neural network application
 */

ENTRY(_start)

SECTIONS
{
    /* UEFI applications start at image base */
    . = 0x10000000;
    
    /* Code section */
    .text : {
        *(.text)
        *(.text.*)
    }
    
    /* Read-only data */
    .rodata : {
        *(.rodata)
        *(.rodata.*)
    }
    
    /* Initialized data */
    .data : {
        *(.data)
        *(.data.*)
    }
    
    /* Small data section */
    .sdata : {
        *(.sdata)
        *(.sdata.*)
    }
    
    /* Uninitialized data */
    .bss : {
        *(.bss)
        *(.bss.*)
        *(COMMON)
    }
    
    /* Dynamic linking sections */
    .dynamic : {
        *(.dynamic)
    }
    
    .dynsym : {
        *(.dynsym)
    }
    
    .dynstr : {
        *(.dynstr)
    }
    
    /* Relocation sections */
    .rel : {
        *(.rel.*)
    }
    
    .rela : {
        *(.rela.*)
    }
    
    .reloc : {
        *(.reloc)
    }
    
    /* Neural network data sections */
    .neural_weights : {
        . = ALIGN(4096);
        neural_weights_start = .;
        . += 0x40000;  /* 256KB for weights and biases */
        neural_weights_end = .;
    }
    
    .neural_activations : {
        . = ALIGN(4096);
        neural_activations_start = .;
        . += 0x10000;  /* 64KB for layer activations */
        neural_activations_end = .;
    }
    
    .neural_gradients : {
        . = ALIGN(4096);
        neural_gradients_start = .;
        . += 0x10000;  /* 64KB for gradients */
        neural_gradients_end = .;
    }
    
    .system_monitoring : {
        . = ALIGN(4096);
        system_monitoring_start = .;
        . += 0x20000;  /* 128KB for system monitoring data */
        system_monitoring_end = .;
    }
    
    .lookup_tables : {
        . = ALIGN(4096);
        lookup_tables_start = .;
        . += 0x80000;  /* 512KB for activation lookup tables */
        lookup_tables_end = .;
    }
    
    /* Stack space */
    .stack : {
        . = ALIGN(4096);
        stack_bottom = .;
        . += 0x10000;  /* 64KB stack */
        stack_top = .;
    }
    
    /* Heap space for dynamic allocations */
    .heap : {
        . = ALIGN(4096);
        heap_start = .;
        . += 0x100000; /* 1MB heap */
        heap_end = .;
    }
    
    /* Discard debug sections */
    /DISCARD/ : {
        *(.debug*)
        *(.comment)
        *(.note*)
    }
}
