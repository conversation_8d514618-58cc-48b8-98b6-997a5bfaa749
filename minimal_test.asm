; Minimal UEFI Test Application
; This is the simplest possible UEFI application to test if our build process works

[BITS 64]

section .text

global _start

; UEFI Application Entry Point
; Parameters: RCX = ImageHandle, RDX = SystemTable
_start:
    ; Simple test - just return success
    ; This is the absolute minimum UEFI application
    mov rax, 0    ; EFI_SUCCESS
    ret

section .data

; Minimal data section
test_data db 0
