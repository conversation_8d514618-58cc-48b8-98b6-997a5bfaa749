#!/bin/bash
# Project Obsoletion - Direct Boot Script (No startup.nsh)
# This script uses QEMU's direct EFI execution to bypass UEFI shell entirely

set -e

echo "╔══════════════════════════════════════════════════════════════╗"
echo "║              PROJECT OBSOLETION DIRECT BOOT                 ║"
echo "║           Bypassing UEFI Shell - Direct AI LLM Launch       ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo ""

# Configuration
OBSOLETION_EFI="obsoletion_enhanced.efi"
OVMF_PATH="/usr/share/ovmf/OVMF.fd"

# Function to find Project Obsoletion EFI file
find_obsoletion_efi() {
    local search_paths=(
        "obsoletion_enhanced.efi"
        "obsoletion.efi"
        "obsoletion/source/obsoletion_enhanced.efi"
        "source/obsoletion_enhanced.efi"
        "obsoletion_complete/obsoletion_enhanced.efi"
    )
    
    for path in "${search_paths[@]}"; do
        if [[ -f "$path" ]]; then
            OBSOLETION_EFI="$path"
            echo "✓ Found Project Obsoletion at: $path"
            return 0
        fi
    done
    
    echo "✗ ERROR: Project Obsoletion EFI file not found!"
    echo "  Please ensure the project is built and one of these files exists:"
    printf "  - %s\n" "${search_paths[@]}"
    return 1
}

# Function to launch QEMU with direct EFI execution
launch_qemu_direct() {
    echo ""
    echo "Starting Project Obsoletion with direct EFI boot..."
    echo "This method bypasses the UEFI shell entirely."
    echo "Press Ctrl+C to exit when ready."
    echo ""
    echo "Expected behavior:"
    echo "1. UEFI firmware loads"
    echo "2. Project Obsoletion EFI executes directly"
    echo "3. Neural network initializes immediately"
    echo "4. AI LLM begins operation"
    echo ""
    
    # Create a temporary directory for the EFI file
    local temp_dir=$(mktemp -d)
    local efi_copy="$temp_dir/BOOTX64.EFI"
    
    # Copy the EFI file to the standard boot location name
    cp "$OBSOLETION_EFI" "$efi_copy"
    
    echo "Launching QEMU with direct EFI execution..."
    
    # Launch QEMU with the EFI file as a direct boot option
    qemu-system-x86_64 \
        -bios "$OVMF_PATH" \
        -drive format=raw,file=fat:rw:"$temp_dir" \
        -m 2048 \
        -smp 2 \
        -boot order=c \
        -boot menu=off \
        -serial stdio \
        -monitor none \
        -nographic \
        -no-reboot \
        -no-shutdown
    
    # Cleanup
    rm -rf "$temp_dir"
}

# Function to launch with FAT filesystem method
launch_qemu_fat() {
    echo ""
    echo "Starting Project Obsoletion with FAT filesystem method..."
    echo "This provides direct access to the current directory."
    echo "Press Ctrl+C to exit when ready."
    echo ""
    
    # Create a simple startup script in current directory
    cat > startup.nsh << 'EOF'
@echo -off
cls
echo [OBSOLETION] Direct boot - Starting AI LLM...
if exist obsoletion_enhanced.efi then
    obsoletion_enhanced.efi
endif
if exist obsoletion.efi then
    obsoletion.efi
endif
echo [OBSOLETION] Manual boot required - type: obsoletion_enhanced.efi
EOF
    
    echo "Launching QEMU with FAT filesystem access..."
    
    # Launch QEMU with direct filesystem access
    qemu-system-x86_64 \
        -bios "$OVMF_PATH" \
        -drive format=raw,file=fat:rw:. \
        -m 2048 \
        -smp 2 \
        -boot order=c \
        -boot menu=off \
        -serial stdio \
        -monitor none \
        -nographic \
        -no-reboot \
        -no-shutdown
}

# Main execution
main() {
    # Check prerequisites
    if ! command -v qemu-system-x86_64 &> /dev/null; then
        echo "✗ ERROR: QEMU not found. Please install: sudo apt install qemu-system-x86"
        exit 1
    fi
    
    if [[ ! -f "$OVMF_PATH" ]]; then
        echo "✗ ERROR: OVMF firmware not found. Please install: sudo apt install ovmf"
        exit 1
    fi
    
    # Find Project Obsoletion
    if ! find_obsoletion_efi; then
        exit 1
    fi
    
    # Ask user which method to use
    echo ""
    echo "Choose boot method:"
    echo "1. Direct EFI Boot (Recommended - bypasses UEFI shell)"
    echo "2. FAT Filesystem Boot (Alternative method)"
    echo ""
    read -p "Enter choice (1 or 2) [default: 1]: " choice
    
    case "${choice:-1}" in
        1)
            launch_qemu_direct
            ;;
        2)
            launch_qemu_fat
            ;;
        *)
            echo "Invalid choice. Using direct EFI boot."
            launch_qemu_direct
            ;;
    esac
    
    echo ""
    echo "Project Obsoletion session ended."
    echo "Thank you for using the revolutionary AI LLM system!"
}

# Run main function
main "$@"
