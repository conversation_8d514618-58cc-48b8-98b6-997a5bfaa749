
-- REIGN Code Companion Integration
-- This Lu<PERSON> script connects Code Companion to REIGN neural core

local reign_interface = {}

function reign_interface.query_reign(prompt, context)
    -- Call Python bridge to query REIGN
    local handle = io.popen('python3 -c "from reign_integration_bridge import ReignNeuralInterface; r=ReignNeuralInterface(); r.start_reign_core(); import asyncio; print(asyncio.run(r.query_reign(\"' .. prompt .. '\")))"')
    local result = handle:read("*a")
    handle:close()
    return result
end

function reign_interface.code_completion(code, cursor_pos)
    local prompt = "Complete this code: " .. code
    return reign_interface.query_reign(prompt, {type = "code_completion"})
end

function reign_interface.code_explanation(code)
    local prompt = "Explain this code: " .. code
    return reign_interface.query_reign(prompt, {type = "code_explanation"})
end

function reign_interface.code_refactor(code)
    local prompt = "Refactor this code: " .. code
    return reign_interface.query_reign(prompt, {type = "code_refactor"})
end

return reign_interface
