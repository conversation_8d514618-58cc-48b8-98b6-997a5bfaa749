## Description

<!-- Describe the big picture of your changes to communicate to the maintainers why we should accept this pull request. -->

## Related Issue(s)

<!--
  If this PR fixes any issues, please link to the issue here.
  - Fixes #<issue_number>
-->

## Screenshots

<!-- Add screenshots of the changes if applicable, to help visualize the change. -->

## Checklist

- [ ] I've read the [contributing](https://github.com/olimorris/codecompanion.nvim/blob/main/CONTRIBUTING.md) guidelines and have adhered to them in this PR
- [ ] I've updated `CodeCompanion.has` in the [init.lua](https://github.com/olimorris/codecompanion.nvim/blob/main/lua/codecompanion/init.lua#L239) file for my new feature
- [ ] I've added [test](https://github.com/olimorris/codecompanion.nvim/blob/main/CONTRIBUTING.md#testing) coverage for this fix/feature
- [ ] I've updated the README and/or relevant docs pages
- [ ] I've run `make all` to ensure docs are generated, tests pass and my formatting is applied
