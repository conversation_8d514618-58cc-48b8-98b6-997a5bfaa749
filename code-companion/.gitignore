# Compiled Lua sources
luac.out

# luarocks build files
*.src.rock
*.zip
*.tar.gz

# Object files
*.o
*.os
*.ko
*.obj
*.elf

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
*.a
*.la
*.lo
*.def
*.exp

# Shared objects (inc. Windows DLLs)
*.dll
*.so
*.so.*
*.dylib

# Executables
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# Macos
.DS_Store

# vitepress
doc/.vitepress/dist
doc/.vitepress/cache
doc/tags*
doc/node_modules

assets/
.direnv/
.testenv/
.repro/
deps/
todo.md
/media/
/examples/
chroma.log
