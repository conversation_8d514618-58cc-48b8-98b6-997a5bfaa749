#!/bin/bash
# Test the simple obsoletion.efi file

echo "╔══════════════════════════════════════════════════════════════╗"
echo "║              TESTING SIMPLE OBSOLETION.EFI                  ║"
echo "║           Diagnosing EFI Execution Issues                   ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo ""

# Check if simple EFI exists
if [[ ! -f "obsoletion.efi" ]]; then
    echo "✗ obsoletion.efi not found!"
    exit 1
fi

echo "✓ Found obsoletion.efi ($(stat -c%s obsoletion.efi) bytes)"

# Create test directory
mkdir -p test_simple_efi/EFI/BOOT
cp obsoletion.efi test_simple_efi/EFI/BOOT/BOOTX64.EFI
cp obsoletion.efi test_simple_efi/obsoletion.efi

# Create simple startup script
cat > test_simple_efi/startup.nsh << 'EOF'
@echo -off
cls
echo Testing Simple Project Obsoletion EFI...
echo.
echo Attempting execution...
if exist obsoletion.efi then
    echo Found obsoletion.efi - executing...
    obsoletion.efi
else
    echo obsoletion.efi not found
endif
echo.
echo Attempting BOOTX64.EFI...
if exist fs0:\EFI\BOOT\BOOTX64.EFI then
    echo Found BOOTX64.EFI - executing...
    fs0:\EFI\BOOT\BOOTX64.EFI
else
    echo BOOTX64.EFI not found
endif
echo.
echo Test completed. Manual execution: obsoletion.efi
EOF

echo "Starting QEMU with simple EFI test..."
echo "This will help us determine if the issue is with the EFI file or the boot process."
echo ""

qemu-system-x86_64 \
    -bios /usr/share/ovmf/OVMF.fd \
    -drive format=raw,file=fat:rw:test_simple_efi \
    -m 1024 \
    -smp 1 \
    -boot order=c \
    -boot menu=off \
    -serial stdio \
    -monitor none \
    -nographic \
    -no-reboot

# Cleanup
rm -rf test_simple_efi

echo ""
echo "Test completed."
