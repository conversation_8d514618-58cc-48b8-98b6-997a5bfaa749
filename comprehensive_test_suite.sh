#!/bin/bash
# Project Obsoletion - Comprehensive Test Suite
# Exhaustive testing of all components and integrations
# Author: Augment Agent

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
WARNINGS=0

# Logging
TEST_LOG="comprehensive_test_results.log"
echo "=== Project Obsoletion Comprehensive Test Suite ===" > $TEST_LOG
echo "Started: $(date)" >> $TEST_LOG
echo "" >> $TEST_LOG

print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${<PERSON><PERSON><PERSON>}$1${NC}"
    echo -e "${CYAN}================================${NC}"
    echo "$1" >> $TEST_LOG
}

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
    echo "[TEST] $1" >> $TEST_LOG
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
}

print_pass() {
    echo -e "${GREEN}[PASS]${NC} $1"
    echo "[PASS] $1" >> $TEST_LOG
    PASSED_TESTS=$((PASSED_TESTS + 1))
}

print_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
    echo "[FAIL] $1" >> $TEST_LOG
    FAILED_TESTS=$((FAILED_TESTS + 1))
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    echo "[WARN] $1" >> $TEST_LOG
    WARNINGS=$((WARNINGS + 1))
}

print_info() {
    echo -e "${PURPLE}[INFO]${NC} $1"
    echo "[INFO] $1" >> $TEST_LOG
}

# Test 1: Environment and Dependencies
test_environment() {
    print_header "TESTING ENVIRONMENT AND DEPENDENCIES"
    
    print_test "Checking NASM assembler"
    if command -v nasm &> /dev/null; then
        NASM_VERSION=$(nasm -v)
        print_pass "NASM found: $NASM_VERSION"
    else
        print_fail "NASM not found"
        return 1
    fi
    
    print_test "Checking GNU linker"
    if command -v ld &> /dev/null; then
        LD_VERSION=$(ld --version | head -n1)
        print_pass "GNU ld found: $LD_VERSION"
    else
        print_fail "GNU ld not found"
        return 1
    fi
    
    print_test "Checking objcopy"
    if command -v objcopy &> /dev/null; then
        OBJCOPY_VERSION=$(objcopy --version | head -n1)
        print_pass "objcopy found: $OBJCOPY_VERSION"
    else
        print_fail "objcopy not found"
        return 1
    fi
    
    print_test "Checking QEMU"
    if command -v qemu-system-x86_64 &> /dev/null; then
        QEMU_VERSION=$(qemu-system-x86_64 --version | head -n1)
        print_pass "QEMU found: $QEMU_VERSION"
    else
        print_warn "QEMU not found - runtime testing will be limited"
    fi
    
    print_test "Checking ARM cross-compiler"
    if command -v aarch64-linux-gnu-as &> /dev/null; then
        ARM_VERSION=$(aarch64-linux-gnu-as --version | head -n1)
        print_pass "ARM cross-compiler found: $ARM_VERSION"
    else
        print_warn "ARM cross-compiler not found - ARM builds will fail"
    fi
}

# Test 2: File Structure and Integrity
test_file_structure() {
    print_header "TESTING FILE STRUCTURE AND INTEGRITY"
    
    # Core system files
    CORE_FILES=(
        "obsoletion_main.asm"
        "firmware_ai_core.asm"
        "ai_consciousness_engine.asm"
        "system_control_engine.asm"
        "adaptive_learning_engine.asm"
    )
    
    # Phase 3 enhancement files
    ENHANCEMENT_FILES=(
        "advanced_neural_engine.asm"
        "security_fortification.asm"
        "universal_compatibility.asm"
        "arm_neural_core.asm"
        "neural_support_functions.asm"
    )
    
    # UEFI implementation files
    UEFI_FILES=(
        "uefi_bootloader_simple.asm"
        "uefi_neural_core.asm"
        "uefi_system_hooks.asm"
        "uefi_service_hooks.asm"
    )
    
    # Legacy implementation files
    LEGACY_FILES=(
        "bootloader_minimal.asm"
        "neural_core.asm"
        "syscall_hook.asm"
        "rag_system.asm"
    )
    
    # Build system files
    BUILD_FILES=(
        "Makefile"
        "uefi_linker.ld"
        "linker.ld"
        "test_build.sh"
    )
    
    # Documentation files
    DOC_FILES=(
        "README.md"
        "ADVANCED_SYSTEM_DOCS.md"
        "PHASE3_ENHANCEMENTS.md"
        "TEST_RESULTS.md"
        "COMPREHENSIVE_FINAL_STATUS.md"
        "BUILD_SUCCESS_REPORT.md"
    )
    
    test_file_group() {
        local group_name=$1
        shift
        local files=("$@")
        
        print_test "Checking $group_name files"
        local missing_files=0
        
        for file in "${files[@]}"; do
            if [[ -f "$file" ]]; then
                local size=$(stat -c%s "$file")
                print_info "  ✓ $file ($size bytes)"
            else
                print_fail "  ✗ Missing: $file"
                missing_files=$((missing_files + 1))
            fi
        done
        
        if [[ $missing_files -eq 0 ]]; then
            print_pass "$group_name files complete"
        else
            print_fail "$missing_files missing $group_name files"
        fi
    }
    
    test_file_group "Core System" "${CORE_FILES[@]}"
    test_file_group "Phase 3 Enhancement" "${ENHANCEMENT_FILES[@]}"
    test_file_group "UEFI Implementation" "${UEFI_FILES[@]}"
    test_file_group "Legacy Implementation" "${LEGACY_FILES[@]}"
    test_file_group "Build System" "${BUILD_FILES[@]}"
    test_file_group "Documentation" "${DOC_FILES[@]}"
}

# Test 3: Assembly Syntax Validation
test_assembly_syntax() {
    print_header "TESTING ASSEMBLY SYNTAX VALIDATION"
    
    ASM_FILES=(
        "obsoletion_main.asm"
        "firmware_ai_core.asm"
        "ai_consciousness_engine.asm"
        "system_control_engine.asm"
        "adaptive_learning_engine.asm"
        "advanced_neural_engine.asm"
        "security_fortification.asm"
        "universal_compatibility.asm"
        "neural_support_functions.asm"
        "uefi_bootloader_simple.asm"
        "uefi_neural_core.asm"
        "uefi_system_hooks.asm"
        "uefi_service_hooks.asm"
        "bootloader_minimal.asm"
        "neural_core.asm"
        "syscall_hook.asm"
        "rag_system.asm"
    )
    
    for asm_file in "${ASM_FILES[@]}"; do
        if [[ -f "$asm_file" ]]; then
            print_test "Syntax check: $asm_file"
            
            # Check for basic syntax issues
            if nasm -f elf64 -o /tmp/test_syntax.o "$asm_file" 2>/dev/null; then
                print_pass "Syntax valid: $asm_file"
                rm -f /tmp/test_syntax.o
            else
                print_fail "Syntax errors in: $asm_file"
                nasm -f elf64 -o /tmp/test_syntax.o "$asm_file" 2>&1 | head -5 >> $TEST_LOG
            fi
        else
            print_warn "File not found for syntax check: $asm_file"
        fi
    done
}

# Test 4: Build System Validation
test_build_system() {
    print_header "TESTING BUILD SYSTEM VALIDATION"
    
    print_test "Makefile syntax validation"
    if make -n clean &>/dev/null; then
        print_pass "Makefile syntax valid"
    else
        print_fail "Makefile syntax errors"
        make -n clean 2>&1 | head -5 >> $TEST_LOG
    fi
    
    print_test "Clean build environment"
    if make clean &>/dev/null; then
        print_pass "Clean successful"
    else
        print_fail "Clean failed"
    fi
    
    # Test individual component builds
    COMPONENTS=(
        "neural_support_functions.o"
        "advanced_neural_engine.o"
        "uefi_bootloader.o"
    )
    
    for component in "${COMPONENTS[@]}"; do
        print_test "Building component: $component"
        if make "$component" &>/dev/null; then
            print_pass "Built successfully: $component"
            if [[ -f "$component" ]]; then
                local size=$(stat -c%s "$component")
                print_info "  Size: $size bytes"
            fi
        else
            print_fail "Build failed: $component"
            make "$component" 2>&1 | tail -10 >> $TEST_LOG
        fi
    done
}

# Test 5: Complete System Builds
test_complete_builds() {
    print_header "TESTING COMPLETE SYSTEM BUILDS"
    
    print_test "Enhanced UEFI application build"
    if make enhanced &>/dev/null; then
        print_pass "Enhanced UEFI build successful"
        if [[ -f "obsoletion_enhanced.efi" ]]; then
            local size=$(stat -c%s "obsoletion_enhanced.efi")
            print_info "  Enhanced UEFI size: $size bytes"
        fi
    else
        print_fail "Enhanced UEFI build failed"
        make enhanced 2>&1 | tail -15 >> $TEST_LOG
    fi
    
    print_test "Legacy bootloader build"
    if make bootloader.bin &>/dev/null; then
        print_pass "Legacy bootloader build successful"
        if [[ -f "bootloader.bin" ]]; then
            local size=$(stat -c%s "bootloader.bin")
            print_info "  Legacy bootloader size: $size bytes"
            if [[ $size -eq 512 ]]; then
                print_pass "Perfect MBR size (512 bytes)"
            else
                print_warn "Bootloader size not 512 bytes: $size"
            fi
        fi
    else
        print_fail "Legacy bootloader build failed"
        make bootloader.bin 2>&1 | tail -10 >> $TEST_LOG
    fi
    
    print_test "ARM architecture build"
    if command -v aarch64-linux-gnu-as &> /dev/null; then
        if make arm &>/dev/null; then
            print_pass "ARM build successful"
            if [[ -f "obsoletion_arm.bin" ]]; then
                local size=$(stat -c%s "obsoletion_arm.bin")
                print_info "  ARM binary size: $size bytes"
            fi
        else
            print_fail "ARM build failed"
            make arm 2>&1 | tail -10 >> $TEST_LOG
        fi
    else
        print_warn "ARM cross-compiler not available - skipping ARM build test"
    fi
}

# Test 6: Binary Analysis and Validation
test_binary_analysis() {
    print_header "TESTING BINARY ANALYSIS AND VALIDATION"
    
    if [[ -f "obsoletion_enhanced.efi" ]]; then
        print_test "Enhanced UEFI binary analysis"
        
        # Check file type
        local file_type=$(file obsoletion_enhanced.efi)
        print_info "File type: $file_type"
        
        # Check for PE/COFF format (UEFI requirement)
        if echo "$file_type" | grep -q "PE32+"; then
            print_pass "Correct PE32+ format for UEFI"
        else
            print_warn "May not be proper UEFI PE32+ format"
        fi
        
        # Check binary size
        local size=$(stat -c%s "obsoletion_enhanced.efi")
        if [[ $size -gt 1000000 ]]; then  # > 1MB
            print_pass "Substantial binary size indicates full implementation"
        else
            print_warn "Binary size smaller than expected: $size bytes"
        fi
        
        # Check for executable sections
        if objdump -h obsoletion_enhanced.efi &>/dev/null; then
            print_pass "Binary has readable sections"
            local sections=$(objdump -h obsoletion_enhanced.efi | grep -c "CONTENTS")
            print_info "Number of sections with content: $sections"
        else
            print_warn "Cannot analyze binary sections"
        fi
    else
        print_fail "Enhanced UEFI binary not found for analysis"
    fi
    
    if [[ -f "bootloader.bin" ]]; then
        print_test "Legacy bootloader binary analysis"
        
        local size=$(stat -c%s "bootloader.bin")
        if [[ $size -eq 512 ]]; then
            print_pass "Perfect MBR size (512 bytes)"
        else
            print_fail "Incorrect MBR size: $size bytes (should be 512)"
        fi
        
        # Check for boot signature
        local signature=$(xxd -l 2 -s 510 bootloader.bin | awk '{print $2}')
        if [[ "$signature" == "55aa" ]]; then
            print_pass "Correct boot signature (0x55AA)"
        else
            print_warn "Boot signature not found or incorrect: $signature"
        fi
        
        # Check for executable code
        local first_bytes=$(xxd -l 16 bootloader.bin | head -1)
        print_info "First 16 bytes: $first_bytes"
        
    else
        print_fail "Legacy bootloader binary not found for analysis"
    fi
}

# Test 7: Runtime Testing with QEMU
test_runtime_qemu() {
    print_header "TESTING RUNTIME EXECUTION WITH QEMU"
    
    if ! command -v qemu-system-x86_64 &> /dev/null; then
        print_warn "QEMU not available - skipping runtime tests"
        return
    fi
    
    if [[ -f "obsoletion_enhanced.efi" ]]; then
        print_test "UEFI runtime test with OVMF"
        
        # Check for OVMF firmware
        OVMF_PATHS=(
            "/usr/share/ovmf/OVMF.fd"
            "/usr/share/edk2-ovmf/OVMF.fd"
            "/usr/share/qemu/OVMF.fd"
        )
        
        OVMF_FOUND=""
        for ovmf_path in "${OVMF_PATHS[@]}"; do
            if [[ -f "$ovmf_path" ]]; then
                OVMF_FOUND="$ovmf_path"
                break
            fi
        done
        
        if [[ -n "$OVMF_FOUND" ]]; then
            print_info "Using OVMF firmware: $OVMF_FOUND"
            
            # Create temporary directory for UEFI testing
            mkdir -p /tmp/uefi_test
            cp obsoletion_enhanced.efi /tmp/uefi_test/
            
            # Test UEFI boot (5 second timeout)
            timeout 5s qemu-system-x86_64 \
                -bios "$OVMF_FOUND" \
                -drive format=raw,file=fat:rw:/tmp/uefi_test \
                -nographic \
                -serial stdio &>/dev/null
            
            local exit_code=$?
            if [[ $exit_code -eq 124 ]]; then  # Timeout exit code
                print_pass "UEFI system boots successfully (timed out as expected)"
            elif [[ $exit_code -eq 0 ]]; then
                print_pass "UEFI system executed successfully"
            else
                print_warn "UEFI system may have issues (exit code: $exit_code)"
            fi
            
            rm -rf /tmp/uefi_test
        else
            print_warn "OVMF firmware not found - cannot test UEFI boot"
        fi
    fi
    
    if [[ -f "bootloader.bin" ]]; then
        print_test "Legacy bootloader runtime test"
        
        # Test legacy boot (3 second timeout)
        timeout 3s qemu-system-i386 \
            -drive format=raw,file=bootloader.bin \
            -nographic &>/dev/null
        
        local exit_code=$?
        if [[ $exit_code -eq 124 ]]; then  # Timeout exit code
            print_pass "Legacy bootloader boots successfully (timed out as expected)"
        elif [[ $exit_code -eq 0 ]]; then
            print_pass "Legacy bootloader executed successfully"
        else
            print_warn "Legacy bootloader may have issues (exit code: $exit_code)"
        fi
    fi
}

# Test 8: Code Quality Analysis
test_code_quality() {
    print_header "TESTING CODE QUALITY ANALYSIS"
    
    print_test "Assembly code complexity analysis"
    
    local total_lines=0
    local total_functions=0
    local total_labels=0
    
    for asm_file in *.asm; do
        if [[ -f "$asm_file" ]]; then
            local lines=$(wc -l < "$asm_file")
            local functions=$(grep -c "^[a-zA-Z_][a-zA-Z0-9_]*:" "$asm_file" || true)
            local labels=$(grep -c "^\." "$asm_file" || true)
            
            total_lines=$((total_lines + lines))
            total_functions=$((total_functions + functions))
            total_labels=$((total_labels + labels))
            
            print_info "$asm_file: $lines lines, $functions functions, $labels labels"
        fi
    done
    
    print_pass "Total: $total_lines lines, $total_functions functions, $total_labels labels"
    
    print_test "Documentation coverage analysis"
    local doc_files=$(ls *.md 2>/dev/null | wc -l)
    local asm_files=$(ls *.asm 2>/dev/null | wc -l)
    
    if [[ $doc_files -ge 5 ]]; then
        print_pass "Comprehensive documentation ($doc_files MD files)"
    else
        print_warn "Limited documentation ($doc_files MD files)"
    fi
    
    print_test "Build system completeness"
    if [[ -f "Makefile" ]] && [[ -f "uefi_linker.ld" ]] && [[ -f "linker.ld" ]]; then
        print_pass "Complete build system present"
    else
        print_fail "Incomplete build system"
    fi
}

# Test 9: Integration Testing
test_integration() {
    print_header "TESTING SYSTEM INTEGRATION"
    
    print_test "Neural network parameter count validation"
    
    # Analyze neural network files for parameter counts
    local param_files=(
        "advanced_neural_engine.asm"
        "neural_support_functions.asm"
        "ai_consciousness_engine.asm"
    )
    
    local found_params=false
    for file in "${param_files[@]}"; do
        if [[ -f "$file" ]] && grep -q "50000\|TOTAL_WEIGHT_COUNT" "$file"; then
            print_pass "Neural network parameters defined in $file"
            found_params=true
        fi
    done
    
    if [[ "$found_params" == "true" ]]; then
        print_pass "Neural network parameter definitions found"
    else
        print_warn "Neural network parameter definitions not clearly found"
    fi
    
    print_test "Security feature integration"
    if [[ -f "security_fortification.asm" ]]; then
        if grep -q "AES\|RSA\|SHA" "security_fortification.asm"; then
            print_pass "Cryptographic features integrated"
        else
            print_warn "Cryptographic features not clearly integrated"
        fi
    else
        print_fail "Security fortification file missing"
    fi
    
    print_test "Cross-platform compatibility"
    if [[ -f "universal_compatibility.asm" ]] && [[ -f "arm_neural_core.asm" ]]; then
        print_pass "Cross-platform files present"
    else
        print_warn "Cross-platform compatibility files missing"
    fi
}

# Test 10: Performance and Memory Analysis
test_performance() {
    print_header "TESTING PERFORMANCE AND MEMORY ANALYSIS"
    
    print_test "Binary size analysis"
    
    if [[ -f "obsoletion_enhanced.efi" ]]; then
        local size=$(stat -c%s "obsoletion_enhanced.efi")
        print_info "Enhanced UEFI size: $size bytes ($(echo "scale=2; $size/1024/1024" | bc)MB)"
        
        if [[ $size -gt 1000000 ]] && [[ $size -lt 50000000 ]]; then  # 1MB - 50MB
            print_pass "Reasonable binary size for neural network implementation"
        else
            print_warn "Binary size outside expected range"
        fi
    fi
    
    print_test "Memory layout analysis"
    if [[ -f "uefi_linker.ld" ]]; then
        if grep -q "neural_weights\|neural_activations" "uefi_linker.ld"; then
            print_pass "Neural network memory sections defined"
        else
            print_warn "Neural network memory sections not found in linker script"
        fi
    fi
    
    print_test "Stack and heap allocation"
    if [[ -f "uefi_linker.ld" ]]; then
        if grep -q "stack\|heap" "uefi_linker.ld"; then
            print_pass "Stack and heap sections defined"
        else
            print_warn "Stack and heap sections not clearly defined"
        fi
    fi
}

# Main test execution
main() {
    print_header "PROJECT OBSOLETION COMPREHENSIVE TEST SUITE"
    echo "Starting comprehensive testing of all components..."
    echo ""
    
    # Execute all test suites
    test_environment
    test_file_structure
    test_assembly_syntax
    test_build_system
    test_complete_builds
    test_binary_analysis
    test_runtime_qemu
    test_code_quality
    test_integration
    test_performance
    
    # Final results
    print_header "COMPREHENSIVE TEST RESULTS SUMMARY"
    echo -e "${CYAN}Total Tests: $TOTAL_TESTS${NC}"
    echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
    echo -e "${RED}Failed: $FAILED_TESTS${NC}"
    echo -e "${YELLOW}Warnings: $WARNINGS${NC}"
    echo ""
    
    local success_rate=$(echo "scale=1; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc)
    echo -e "${CYAN}Success Rate: $success_rate%${NC}"
    
    echo "" >> $TEST_LOG
    echo "=== FINAL SUMMARY ===" >> $TEST_LOG
    echo "Total Tests: $TOTAL_TESTS" >> $TEST_LOG
    echo "Passed: $PASSED_TESTS" >> $TEST_LOG
    echo "Failed: $FAILED_TESTS" >> $TEST_LOG
    echo "Warnings: $WARNINGS" >> $TEST_LOG
    echo "Success Rate: $success_rate%" >> $TEST_LOG
    echo "Completed: $(date)" >> $TEST_LOG
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        echo -e "${GREEN}🎉 ALL CRITICAL TESTS PASSED! 🎉${NC}"
        echo "System is ready for deployment!"
        return 0
    else
        echo -e "${RED}❌ SOME TESTS FAILED ❌${NC}"
        echo "Please review the test log: $TEST_LOG"
        return 1
    fi
}

# Execute main function
main "$@"
