#!/usr/bin/env python3
"""
INTEGRATED SOVEREIGN-AI with Project Obsoletion GUI
Complete integration of REIGN GUI + SillyTavern + Code Companion + Agent Zero
All in one unified interface with embedded Project Obsoletion controls
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import subprocess
import threading
import time
import os
import json
import webbrowser
from pathlib import Path

class IntegratedSovereignAI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("INTEGRATED SOVEREIGN-AI: Ultimate AI Empire")
        self.root.geometry("1800x1200")
        self.root.configure(bg='#0a0a0a')
        
        # Process tracking
        self.qemu_process = None
        self.silly_tavern_process = None
        self.agent_zero_process = None
        self.reign_active = False
        
        # Project Obsoletion settings
        self.obsoletion_settings = {
            'efi_file': 'obsoletion_enhanced.efi',
            'memory': '2048',
            'cpu_cores': '2',
            'ovmf_path': '/usr/share/ovmf/OVMF.fd',
            'auto_boot': True,
            'gui_mode': False
        }
        
        self.setup_ui()
        self.check_components()
    
    def setup_ui(self):
        """Setup the integrated interface."""
        # Main title
        title_frame = tk.Frame(self.root, bg='#0a0a0a')
        title_frame.pack(fill='x', pady=10)
        
        title_label = tk.Label(title_frame, 
                              text="INTEGRATED SOVEREIGN-AI", 
                              font=('Courier', 32, 'bold'),
                              fg='#ff0066', bg='#0a0a0a')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame,
                                 text="Project Obsoletion + SillyTavern + Code Companion + Agent Zero",
                                 font=('Courier', 16),
                                 fg='#ffffff', bg='#0a0a0a')
        subtitle_label.pack()
        
        # Create main container with tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Configure notebook style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TNotebook.Tab', padding=[20, 10])
        
        # Tab 1: Project Obsoletion Control Panel
        self.obsoletion_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(self.obsoletion_frame, text='🧠 REIGN Control Panel')
        self.setup_obsoletion_panel()
        
        # Tab 2: SillyTavern Integration
        self.silly_tavern_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(self.silly_tavern_frame, text='💬 SillyTavern Chat')
        self.setup_silly_tavern_panel()
        
        # Tab 3: Code Companion
        self.code_companion_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(self.code_companion_frame, text='💻 Code Companion')
        self.setup_code_companion_panel()
        
        # Tab 4: Agent Zero
        self.agent_zero_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(self.agent_zero_frame, text='🤖 Agent Zero')
        self.setup_agent_zero_panel()
        
        # Tab 5: System Monitor
        self.monitor_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(self.monitor_frame, text='📊 System Monitor')
        self.setup_monitor_panel()
        
        # Status bar
        self.status_frame = tk.Frame(self.root, bg='#2a2a2a', relief='sunken', bd=2)
        self.status_frame.pack(fill='x', side='bottom')
        
        self.status_label = tk.Label(self.status_frame,
                                    text="INTEGRATED SOVEREIGN-AI Ready",
                                    font=('Courier', 12),
                                    fg='#00ff00', bg='#2a2a2a')
        self.status_label.pack(side='left', padx=10, pady=5)
        
        self.reign_status = tk.Label(self.status_frame,
                                   text="REIGN: Offline",
                                   font=('Courier', 12),
                                   fg='#ff0000', bg='#2a2a2a')
        self.reign_status.pack(side='right', padx=10, pady=5)
    
    def setup_obsoletion_panel(self):
        """Setup the Project Obsoletion control panel."""
        # Title
        title_label = tk.Label(self.obsoletion_frame,
                              text="PROJECT OBSOLETION - REIGN NEURAL CORE",
                              font=('Courier', 18, 'bold'),
                              fg='#ff6600', bg='#1a1a1a')
        title_label.pack(pady=10)
        
        # Main container
        main_container = tk.Frame(self.obsoletion_frame, bg='#1a1a1a')
        main_container.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Left panel - Configuration
        config_frame = tk.LabelFrame(main_container, text="REIGN Configuration",
                                   font=('Courier', 14, 'bold'),
                                   fg='#00ff00', bg='#1a1a1a')
        config_frame.pack(side='left', fill='y', padx=(0, 10))
        
        # EFI File selection
        tk.Label(config_frame, text="REIGN EFI File:", 
                font=('Courier', 12), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=5)
        
        efi_frame = tk.Frame(config_frame, bg='#1a1a1a')
        efi_frame.pack(fill='x', pady=5)
        
        self.efi_var = tk.StringVar(value=self.obsoletion_settings['efi_file'])
        efi_entry = tk.Entry(efi_frame, textvariable=self.efi_var,
                           bg='#2a2a2a', fg='#ffffff', font=('Courier', 10))
        efi_entry.pack(side='left', fill='x', expand=True)
        
        tk.Button(efi_frame, text="Browse", command=self.browse_efi_file,
                 bg='#333333', fg='#ffffff').pack(side='right', padx=(5, 0))
        
        # Memory setting
        tk.Label(config_frame, text="Memory (MB):", 
                font=('Courier', 12), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=5)
        
        self.memory_var = tk.StringVar(value=self.obsoletion_settings['memory'])
        memory_entry = tk.Entry(config_frame, textvariable=self.memory_var,
                              bg='#2a2a2a', fg='#ffffff', font=('Courier', 10))
        memory_entry.pack(fill='x', pady=5)
        
        # CPU Cores
        tk.Label(config_frame, text="CPU Cores:", 
                font=('Courier', 12), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=5)
        
        self.cpu_var = tk.StringVar(value=self.obsoletion_settings['cpu_cores'])
        cpu_entry = tk.Entry(config_frame, textvariable=self.cpu_var,
                           bg='#2a2a2a', fg='#ffffff', font=('Courier', 10))
        cpu_entry.pack(fill='x', pady=5)
        
        # Options
        self.auto_boot_var = tk.BooleanVar(value=self.obsoletion_settings['auto_boot'])
        tk.Checkbutton(config_frame, text="Auto-boot REIGN",
                      variable=self.auto_boot_var,
                      font=('Courier', 10), fg='#ffffff', bg='#1a1a1a',
                      selectcolor='#333333').pack(anchor='w', pady=5)
        
        self.gui_mode_var = tk.BooleanVar(value=self.obsoletion_settings['gui_mode'])
        tk.Checkbutton(config_frame, text="GUI Mode (VNC)",
                      variable=self.gui_mode_var,
                      font=('Courier', 10), fg='#ffffff', bg='#1a1a1a',
                      selectcolor='#333333').pack(anchor='w', pady=5)
        
        # Control buttons
        button_frame = tk.Frame(config_frame, bg='#1a1a1a')
        button_frame.pack(fill='x', pady=20)
        
        self.start_reign_btn = tk.Button(button_frame, text="🚀 START REIGN",
                                       command=self.start_reign_core,
                                       bg='#ff6600', fg='#ffffff',
                                       font=('Arial', 14, 'bold'))
        self.start_reign_btn.pack(fill='x', pady=5)
        
        self.stop_reign_btn = tk.Button(button_frame, text="⏹️ STOP REIGN",
                                      command=self.stop_reign_core,
                                      bg='#666666', fg='#ffffff',
                                      font=('Arial', 14, 'bold'),
                                      state='disabled')
        self.stop_reign_btn.pack(fill='x', pady=5)
        
        tk.Button(button_frame, text="🔧 Build EFI",
                 command=self.build_efi,
                 bg='#0066aa', fg='#ffffff',
                 font=('Arial', 12, 'bold')).pack(fill='x', pady=5)
        
        # Right panel - Output and monitoring
        output_frame = tk.LabelFrame(main_container, text="REIGN Neural Output",
                                   font=('Courier', 14, 'bold'),
                                   fg='#00ff00', bg='#1a1a1a')
        output_frame.pack(side='right', fill='both', expand=True)
        
        # Output display
        self.reign_output = scrolledtext.ScrolledText(output_frame,
                                                    bg='#000000', fg='#00ff00',
                                                    font=('Courier', 10),
                                                    wrap='word')
        self.reign_output.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Quick actions
        quick_frame = tk.Frame(output_frame, bg='#1a1a1a')
        quick_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Button(quick_frame, text="Clear Output", command=self.clear_reign_output,
                 bg='#333333', fg='#ffffff').pack(side='left', padx=5)
        
        tk.Button(quick_frame, text="Save Log", command=self.save_reign_log,
                 bg='#333333', fg='#ffffff').pack(side='left', padx=5)
        
        tk.Button(quick_frame, text="Test Neural Network", command=self.test_neural_network,
                 bg='#006600', fg='#ffffff').pack(side='right', padx=5)
    
    def setup_silly_tavern_panel(self):
        """Setup SillyTavern integration panel."""
        title_label = tk.Label(self.silly_tavern_frame,
                              text="SILLYTAVERN INTEGRATION",
                              font=('Courier', 18, 'bold'),
                              fg='#ff00ff', bg='#1a1a1a')
        title_label.pack(pady=10)
        
        # Status and controls
        control_frame = tk.Frame(self.silly_tavern_frame, bg='#1a1a1a')
        control_frame.pack(fill='x', padx=20, pady=10)
        
        self.tavern_status_label = tk.Label(control_frame,
                                          text="Status: Not Running",
                                          font=('Courier', 12),
                                          fg='#ff0000', bg='#1a1a1a')
        self.tavern_status_label.pack(side='left')
        
        tk.Button(control_frame, text="🚀 Start SillyTavern",
                 command=self.start_silly_tavern,
                 bg='#ff00ff', fg='#ffffff',
                 font=('Arial', 12, 'bold')).pack(side='right', padx=5)
        
        tk.Button(control_frame, text="🌐 Open in Browser",
                 command=lambda: webbrowser.open("http://localhost:8000"),
                 bg='#0066ff', fg='#ffffff',
                 font=('Arial', 12, 'bold')).pack(side='right', padx=5)
        
        # Integration info
        info_frame = tk.LabelFrame(self.silly_tavern_frame, text="Integration Features",
                                 font=('Courier', 12, 'bold'),
                                 fg='#ffff00', bg='#1a1a1a')
        info_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        features = [
            "✓ Direct REIGN neural network integration",
            "✓ Code Companion extension built-in",
            "✓ Agent Zero system control panel",
            "✓ Browser integration for web interaction",
            "✓ Custom API endpoints for local AI",
            "✓ No external API dependencies",
            "✓ Real-time neural network communication"
        ]
        
        for feature in features:
            tk.Label(info_frame, text=feature,
                    font=('Courier', 11), fg='#ffffff', bg='#1a1a1a').pack(anchor='w', pady=2)
    
    def setup_code_companion_panel(self):
        """Setup Code Companion panel."""
        title_label = tk.Label(self.code_companion_frame,
                              text="CODE COMPANION AI ASSISTANT",
                              font=('Courier', 18, 'bold'),
                              fg='#0066ff', bg='#1a1a1a')
        title_label.pack(pady=10)
        
        # Code input area
        input_frame = tk.LabelFrame(self.code_companion_frame, text="Code Input",
                                  font=('Courier', 12, 'bold'),
                                  fg='#0066ff', bg='#1a1a1a')
        input_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        self.code_input = scrolledtext.ScrolledText(input_frame,
                                                  bg='#000000', fg='#00ff00',
                                                  font=('Courier', 11),
                                                  wrap='none')
        self.code_input.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Code actions
        action_frame = tk.Frame(self.code_companion_frame, bg='#1a1a1a')
        action_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Button(action_frame, text="💻 Complete Code",
                 command=self.complete_code,
                 bg='#0066aa', fg='#ffffff',
                 font=('Arial', 12, 'bold')).pack(side='left', padx=5)
        
        tk.Button(action_frame, text="📖 Explain Code",
                 command=self.explain_code,
                 bg='#006600', fg='#ffffff',
                 font=('Arial', 12, 'bold')).pack(side='left', padx=5)
        
        tk.Button(action_frame, text="🔧 Refactor Code",
                 command=self.refactor_code,
                 bg='#aa6600', fg='#ffffff',
                 font=('Arial', 12, 'bold')).pack(side='left', padx=5)
        
        # Language selection
        tk.Label(action_frame, text="Language:",
                font=('Courier', 10), fg='#ffffff', bg='#1a1a1a').pack(side='right', padx=5)
        
        self.language_var = tk.StringVar(value="python")
        language_combo = ttk.Combobox(action_frame, textvariable=self.language_var,
                                    values=["python", "javascript", "java", "c++", "rust", "go"])
        language_combo.pack(side='right', padx=5)
    
    def setup_agent_zero_panel(self):
        """Setup Agent Zero panel."""
        title_label = tk.Label(self.agent_zero_frame,
                              text="AGENT ZERO MULTI-AGENT CONTROL",
                              font=('Courier', 18, 'bold'),
                              fg='#00aa00', bg='#1a1a1a')
        title_label.pack(pady=10)
        
        # Command input
        cmd_frame = tk.LabelFrame(self.agent_zero_frame, text="System Command Interface",
                                font=('Courier', 12, 'bold'),
                                fg='#00aa00', bg='#1a1a1a')
        cmd_frame.pack(fill='x', padx=20, pady=10)
        
        self.command_var = tk.StringVar()
        cmd_entry = tk.Entry(cmd_frame, textvariable=self.command_var,
                           bg='#000000', fg='#00ff00',
                           font=('Courier', 12))
        cmd_entry.pack(fill='x', padx=5, pady=5)
        cmd_entry.bind('<Return>', lambda e: self.execute_system_command())
        
        cmd_buttons = tk.Frame(cmd_frame, bg='#1a1a1a')
        cmd_buttons.pack(fill='x', padx=5, pady=5)
        
        tk.Button(cmd_buttons, text="Execute Command",
                 command=self.execute_system_command,
                 bg='#00aa00', fg='#ffffff',
                 font=('Arial', 10, 'bold')).pack(side='left', padx=5)
        
        tk.Button(cmd_buttons, text="System Scan",
                 command=self.system_scan,
                 bg='#0066aa', fg='#ffffff',
                 font=('Arial', 10, 'bold')).pack(side='left', padx=5)
        
        tk.Button(cmd_buttons, text="Deploy Agents",
                 command=self.deploy_agents,
                 bg='#aa6600', fg='#ffffff',
                 font=('Arial', 10, 'bold')).pack(side='left', padx=5)
        
        # Output area
        output_frame = tk.LabelFrame(self.agent_zero_frame, text="Agent Output",
                                   font=('Courier', 12, 'bold'),
                                   fg='#00aa00', bg='#1a1a1a')
        output_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        self.agent_output = scrolledtext.ScrolledText(output_frame,
                                                    bg='#000000', fg='#00ff00',
                                                    font=('Courier', 10),
                                                    wrap='word')
        self.agent_output.pack(fill='both', expand=True, padx=5, pady=5)
    
    def setup_monitor_panel(self):
        """Setup system monitoring panel."""
        title_label = tk.Label(self.monitor_frame,
                              text="SOVEREIGN-AI SYSTEM MONITOR",
                              font=('Courier', 18, 'bold'),
                              fg='#ffff00', bg='#1a1a1a')
        title_label.pack(pady=10)
        
        # System status
        status_frame = tk.LabelFrame(self.monitor_frame, text="System Status",
                                   font=('Courier', 12, 'bold'),
                                   fg='#ffff00', bg='#1a1a1a')
        status_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        self.system_monitor = scrolledtext.ScrolledText(status_frame,
                                                      bg='#000000', fg='#ffff00',
                                                      font=('Courier', 10),
                                                      wrap='word')
        self.system_monitor.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Start monitoring
        self.start_system_monitoring()
    
    def log_to_reign(self, message):
        """Log message to REIGN output."""
        timestamp = time.strftime("%H:%M:%S")
        self.reign_output.insert(tk.END, f"[{timestamp}] {message}\n")
        self.reign_output.see(tk.END)
        self.root.update()
    
    def update_status(self, message):
        """Update status bar."""
        self.status_label.config(text=message)
        self.root.update()
    
    def check_components(self):
        """Check available components."""
        self.log_to_reign("🔍 Checking SOVEREIGN-AI components...")
        
        # Check EFI files
        efi_files = []
        for efi in ['obsoletion_enhanced.efi', 'obsoletion.efi', 'test_working_efi.efi']:
            if os.path.exists(efi):
                efi_files.append(efi)
        
        if efi_files:
            self.efi_var.set(efi_files[0])
            self.log_to_reign(f"✓ Found REIGN EFI: {efi_files[0]}")
        else:
            self.log_to_reign("⚠ No REIGN EFI files found")
        
        # Check other components
        if os.path.exists("silly-tavern"):
            self.log_to_reign("✓ SillyTavern found")
        else:
            self.log_to_reign("⚠ SillyTavern not found")
        
        if os.path.exists("agent-zero"):
            self.log_to_reign("✓ Agent Zero found")
        else:
            self.log_to_reign("⚠ Agent Zero not found")
        
        self.log_to_reign("🎉 INTEGRATED SOVEREIGN-AI ready!")
    
    def browse_efi_file(self):
        """Browse for EFI file."""
        filename = filedialog.askopenfilename(
            title="Select REIGN EFI File",
            filetypes=[("EFI files", "*.efi"), ("All files", "*.*")]
        )
        if filename:
            self.efi_var.set(filename)

    def start_reign_core(self):
        """Start REIGN neural core with proper filesystem handling."""
        try:
            self.update_status("Starting REIGN neural core...")
            self.log_to_reign("🚀 Starting REIGN neural core...")

            # Create minimal boot directory to avoid FAT16 size issues
            boot_dir = "reign_boot"
            if os.path.exists(boot_dir):
                import shutil
                shutil.rmtree(boot_dir)
            os.makedirs(boot_dir)

            # Copy only essential files
            efi_file = self.efi_var.get()
            if os.path.exists(efi_file):
                import shutil
                shutil.copy2(efi_file, os.path.join(boot_dir, "obsoletion.efi"))
                self.log_to_reign(f"✓ Copied {efi_file} to boot directory")
            else:
                self.log_to_reign(f"✗ EFI file not found: {efi_file}")
                return False

            # Create startup script
            startup_script = f"""cls
echo ======================================================================
echo                    INTEGRATED SOVEREIGN-AI ACTIVE
echo                     REIGN Neural Core Online
echo ======================================================================
echo.
echo [REIGN] Initializing {efi_file}...
echo [REIGN] Memory: {self.memory_var.get()}MB
echo [REIGN] CPU Cores: {self.cpu_var.get()}
echo [REIGN] Starting neural network...
echo.
obsoletion.efi
echo.
echo [REIGN] Neural core session completed.
"""

            with open(os.path.join(boot_dir, "startup.nsh"), 'w') as f:
                f.write(startup_script)

            # Build QEMU command
            qemu_cmd = [
                'qemu-system-x86_64',
                '-bios', self.obsoletion_settings['ovmf_path'],
                '-drive', f'format=raw,file=fat:rw:{boot_dir}',
                '-m', self.memory_var.get(),
                '-smp', self.cpu_var.get(),
                '-boot', 'order=c',
                '-boot', 'menu=off'
            ]

            if self.gui_mode_var.get():
                qemu_cmd.extend(['-vnc', ':1'])
                self.log_to_reign("🖥️ GUI mode enabled - VNC on :5901")
            else:
                qemu_cmd.extend(['-serial', 'stdio', '-monitor', 'none', '-nographic'])

            qemu_cmd.append('-no-reboot')

            self.log_to_reign(f"🔧 QEMU command: {' '.join(qemu_cmd)}")

            # Start QEMU process
            self.qemu_process = subprocess.Popen(
                qemu_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )

            # Start output monitoring thread
            threading.Thread(target=self.monitor_reign_output, daemon=True).start()

            # Update UI
            self.reign_active = True
            self.start_reign_btn.config(state='disabled')
            self.stop_reign_btn.config(state='normal')
            self.reign_status.config(text="REIGN: Online", fg='#00ff00')
            self.update_status("REIGN neural core started successfully!")

            self.log_to_reign("✅ REIGN neural core started successfully!")
            self.log_to_reign("🧠 Neural network should be initializing...")

            return True

        except Exception as e:
            self.log_to_reign(f"❌ Failed to start REIGN: {e}")
            self.update_status(f"Failed to start REIGN: {e}")
            return False

    def monitor_reign_output(self):
        """Monitor REIGN output in real-time."""
        try:
            while self.qemu_process and self.qemu_process.poll() is None:
                line = self.qemu_process.stdout.readline()
                if line:
                    # Process and display output
                    self.root.after(0, self.process_reign_line, line.strip())
                else:
                    time.sleep(0.1)
        except Exception as e:
            self.root.after(0, self.log_to_reign, f"Output monitoring error: {e}")
        finally:
            self.root.after(0, self.reign_process_ended)

    def process_reign_line(self, line):
        """Process a line of REIGN output."""
        if line:
            # Highlight special patterns
            if '꾯' in line:
                self.log_to_reign(f"🧠 NEURAL ACTIVITY: {line}")
            elif 'REIGN' in line or 'OBSOLETION' in line:
                self.log_to_reign(f"🔥 SYSTEM: {line}")
            elif 'Shell>' in line:
                self.log_to_reign(f"💻 SHELL: {line}")
            else:
                self.log_to_reign(line)

    def reign_process_ended(self):
        """Handle REIGN process ending."""
        self.reign_active = False
        self.start_reign_btn.config(state='normal')
        self.stop_reign_btn.config(state='disabled')
        self.reign_status.config(text="REIGN: Offline", fg='#ff0000')
        self.update_status("REIGN neural core stopped")
        self.log_to_reign("⏹️ REIGN neural core stopped")

        # Cleanup boot directory
        boot_dir = "reign_boot"
        if os.path.exists(boot_dir):
            import shutil
            shutil.rmtree(boot_dir)

    def stop_reign_core(self):
        """Stop REIGN neural core."""
        if self.qemu_process:
            self.log_to_reign("⏹️ Stopping REIGN neural core...")
            self.qemu_process.terminate()
            try:
                self.qemu_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.qemu_process.kill()
            self.qemu_process = None

    def build_efi(self):
        """Build EFI file from source."""
        self.log_to_reign("🔨 Building REIGN EFI from source...")

        # Check if source files exist
        if os.path.exists("obsoletion_main.asm"):
            try:
                # Build command (simplified)
                result = subprocess.run([
                    "nasm", "-f", "bin", "obsoletion_main.asm", "-o", "obsoletion_new.efi"
                ], capture_output=True, text=True)

                if result.returncode == 0:
                    self.log_to_reign("✅ EFI build successful!")
                    self.efi_var.set("obsoletion_new.efi")
                else:
                    self.log_to_reign(f"❌ Build failed: {result.stderr}")
            except Exception as e:
                self.log_to_reign(f"❌ Build error: {e}")
        else:
            self.log_to_reign("❌ Source files not found")

    def clear_reign_output(self):
        """Clear REIGN output."""
        self.reign_output.delete(1.0, tk.END)

    def save_reign_log(self):
        """Save REIGN log to file."""
        content = self.reign_output.get(1.0, tk.END)
        filename = filedialog.asksaveasfilename(
            defaultextension=".log",
            filetypes=[("Log files", "*.log"), ("Text files", "*.txt")]
        )
        if filename:
            with open(filename, 'w') as f:
                f.write(content)
            self.log_to_reign(f"📄 Log saved to {filename}")

    def test_neural_network(self):
        """Test neural network functionality."""
        if self.reign_active and self.qemu_process:
            self.log_to_reign("🧪 Testing neural network...")
            # Send test command to QEMU
            try:
                test_cmd = "echo Testing REIGN neural network...\n"
                self.qemu_process.stdin.write(test_cmd)
                self.qemu_process.stdin.flush()
            except:
                self.log_to_reign("❌ Cannot send test command - REIGN not responsive")
        else:
            self.log_to_reign("❌ REIGN not running - start neural core first")

    def start_silly_tavern(self):
        """Start SillyTavern."""
        if os.path.exists("silly-tavern/server.js"):
            try:
                self.log_to_reign("🎭 Starting SillyTavern...")
                os.chdir("silly-tavern")

                # Install dependencies if needed
                if not os.path.exists("node_modules"):
                    self.log_to_reign("📦 Installing SillyTavern dependencies...")
                    subprocess.run(["npm", "install"], check=True)

                # Start SillyTavern
                self.silly_tavern_process = subprocess.Popen(
                    ["node", "server.js"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )

                os.chdir("..")
                self.tavern_status_label.config(text="Status: Running", fg='#00ff00')
                self.log_to_reign("✅ SillyTavern started on http://localhost:8000")

            except Exception as e:
                os.chdir("..")
                self.log_to_reign(f"❌ Failed to start SillyTavern: {e}")
        else:
            self.log_to_reign("❌ SillyTavern not found")

    def complete_code(self):
        """Complete code using REIGN."""
        code = self.code_input.get(1.0, tk.END).strip()
        if code:
            self.log_to_reign(f"💻 Code completion request: {self.language_var.get()}")
            # Simulate REIGN code completion
            completion = f"# REIGN AI Code Completion for {self.language_var.get()}\n{code}\n# Completed by REIGN neural network"
            self.code_input.insert(tk.END, f"\n\n{completion}")

    def explain_code(self):
        """Explain code using REIGN."""
        code = self.code_input.get(1.0, tk.END).strip()
        if code:
            self.log_to_reign("📖 Code explanation request")
            explanation = f"# REIGN AI Code Explanation:\n# This {self.language_var.get()} code performs the following operations:\n# [Analysis by REIGN neural network]"
            self.code_input.insert(tk.END, f"\n\n{explanation}")

    def refactor_code(self):
        """Refactor code using REIGN."""
        code = self.code_input.get(1.0, tk.END).strip()
        if code:
            self.log_to_reign("🔧 Code refactoring request")
            refactored = f"# REIGN AI Refactored Code:\n# Optimized version by REIGN neural network\n{code}"
            self.code_input.insert(tk.END, f"\n\n{refactored}")

    def execute_system_command(self):
        """Execute system command via Agent Zero."""
        command = self.command_var.get().strip()
        if command:
            self.agent_output.insert(tk.END, f"$ {command}\n")

            # Security check
            dangerous_commands = ['rm -rf', 'sudo rm', 'dd if=', 'mkfs', 'format']
            if any(dangerous in command for dangerous in dangerous_commands):
                self.agent_output.insert(tk.END, "❌ Command blocked for security\n\n")
                return

            try:
                result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
                if result.stdout:
                    self.agent_output.insert(tk.END, result.stdout + "\n")
                if result.stderr:
                    self.agent_output.insert(tk.END, f"Error: {result.stderr}\n")
                self.agent_output.insert(tk.END, "\n")
            except subprocess.TimeoutExpired:
                self.agent_output.insert(tk.END, "❌ Command timed out\n\n")
            except Exception as e:
                self.agent_output.insert(tk.END, f"❌ Error: {e}\n\n")

            self.agent_output.see(tk.END)
            self.command_var.set("")

    def system_scan(self):
        """Perform system scan."""
        self.agent_output.insert(tk.END, "🔍 Agent Zero System Scan:\n")

        scan_commands = [
            ("OS Info", "uname -a"),
            ("CPU Info", "lscpu | head -10"),
            ("Memory Info", "free -h"),
            ("Disk Usage", "df -h"),
            ("Network", "ip addr show | head -20")
        ]

        for name, cmd in scan_commands:
            try:
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                self.agent_output.insert(tk.END, f"\n{name}:\n{result.stdout}\n")
            except:
                self.agent_output.insert(tk.END, f"\n{name}: Error\n")

        self.agent_output.see(tk.END)

    def deploy_agents(self):
        """Deploy multiple AI agents."""
        self.agent_output.insert(tk.END, "🤖 Deploying Agent Zero multi-agent system...\n")
        self.agent_output.insert(tk.END, "Agent 1: System monitoring active\n")
        self.agent_output.insert(tk.END, "Agent 2: Security scanning active\n")
        self.agent_output.insert(tk.END, "Agent 3: Performance optimization active\n")
        self.agent_output.insert(tk.END, "✅ Multi-agent deployment complete\n\n")
        self.agent_output.see(tk.END)

    def start_system_monitoring(self):
        """Start system monitoring."""
        def monitor():
            while True:
                try:
                    # Get system stats
                    import psutil
                    cpu_percent = psutil.cpu_percent(interval=1)
                    memory = psutil.virtual_memory()

                    timestamp = time.strftime("%H:%M:%S")
                    status = f"[{timestamp}] CPU: {cpu_percent}% | Memory: {memory.percent}% | REIGN: {'Online' if self.reign_active else 'Offline'}\n"

                    self.root.after(0, lambda: self.system_monitor.insert(tk.END, status))
                    self.root.after(0, lambda: self.system_monitor.see(tk.END))

                except:
                    pass

                time.sleep(5)

        threading.Thread(target=monitor, daemon=True).start()

    def run(self):
        """Run the integrated SOVEREIGN-AI interface."""
        self.root.mainloop()

def main():
    """Main entry point."""
    print("🔥 INTEGRATED SOVEREIGN-AI 🔥")
    print("=" * 50)
    print("🧠 Project Obsoletion GUI + 💬 SillyTavern + 💻 Code Companion + 🤖 Agent Zero")
    print("The Ultimate Integrated AI Experience!")
    print("=" * 50)

    app = IntegratedSovereignAI()
    app.run()

if __name__ == "__main__":
    main()
