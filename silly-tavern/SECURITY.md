# Security Policy

We take the security of this project seriously. If you discover any security vulnerabilities or have concerns regarding the security of this repository, please reach out to us immediately. We appreciate your efforts in responsibly disclosing the issue and will make every effort to address it promptly.

## Reporting a Vulnerability

To report a security vulnerability, please follow these steps:

1. Go to the **Security** tab of this repository on GitHub.
2. Click on **"Report a vulnerability"**.
3. Provide a clear description of the vulnerability and its potential impact. Be as detailed as possible.
4. If applicable, include steps or a PoC (Proof of Concept) to reproduce the vulnerability.
5. Submit the report.

Once we receive the private report notification, we will promptly investigate and assess the reported vulnerability.

Please do not disclose any potential vulnerabilities in public repositories, issue trackers, or forums until we have had a chance to review and address the issue.

## Scope

This security policy applies to all the code and files within this repository and its dependencies actively maintained by us. If you encounter a security issue in a dependency that is not directly maintained by us, please follow responsible disclosure practices and report it to the respective project.

While we strive to ensure the security of this project, please note that there may be limitations on resources, response times, and mitigations.

Thank you for your help in making this project more secure.
