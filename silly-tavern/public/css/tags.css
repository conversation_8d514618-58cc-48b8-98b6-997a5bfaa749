#bulk_tags_div,
#tags_div {
    min-width: 0;
}

.tag_controls {
    display: flex;
    flex-direction: row;
    gap: 5px;
    align-items: center;
}

.tag_view_item {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 6px;
    margin-bottom: 5px;
}

.tag_view_name {
    text-align: left;
}

.tag_view_counter {
    text-align: right;
    flex: 1;
}

.tag_view_color_picker {
    position: relative;
}

.tag_view_color_picker .link_icon {
    position: absolute;
    top: 50%;
    right: 0px;
    opacity: 0.5;
}

.tag_delete {
    padding: 2px 4px;
    color: var(--SmartThemeBodyColor) !important;
}

.tag {
    border-radius: 5px;
    border-style: solid;
    border-width: 1px;
    box-sizing: border-box;
    color: var(--SmartThemeBodyColor);
    background-color: var(--black30a);
    border-color: var(--white50a);
    padding: 0.1rem 0.2rem;
    font-size: calc(var(--mainFontSize) - 5%);
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;
    gap: 10px;
    width: fit-content;
    min-width: 0;
    text-shadow: none !important;
}

.rm_tag_filter .tag:not(.actionable) {
    display: none;
}

.tag.actionable {
    border-radius: 50%;
    aspect-ratio: 1 / 1;
    min-height: calc(var(--mainFontSize) * 2);
    min-width: calc(var(--mainFontSize) * 2);
    font-size: calc(var(--mainFontSize) * 1);
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.tag.actionable.clearAllFilters {
    border: 0;
    background: none;
}

.tag.placeholder-expander {
    cursor: alias;
    border: 0;
}

.tagListHint {
    align-self: center;
    display: flex;
    margin-right: 10px;
}

.tag_remove {
    cursor: pointer;
}

.tags {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 0.2rem;
    align-items: flex-end;
}

#bulkTagsList,
#tagList.tags {
    margin: 5px 0;
}

#bulkTagsList,
#tagList .tag,
#groupTagList .tag {
    opacity: 0.6;
}

#tagList .tag:has(.tag_remove:hover) {
    opacity: 1;
}

#tagList .tag:has(.tag_remove:hover) .tag_name {
    opacity: 0.6;
}

.tags.tags_inline {
    opacity: 0.6;
    column-gap: 0.2rem;
    row-gap: 0.2rem;
    justify-content: flex-start;
    max-height: 66%;
    overflow: hidden;
    flex-basis: 100%;
}

.tag_name {
    text-overflow: ellipsis;
    overflow: hidden;
    text-align: left;
    white-space: nowrap;
    text-shadow: none !important;
}

.tags_inline .tag {
    font-size: calc(var(--mainFontSize) - 15%);
    padding: 0 0.15rem;
}

.rm_tag_controls {
    display: flex;
    column-gap: 10px;
    row-gap: 5px;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-start;
    margin: 5px;
}

.rm_tag_filter .tag {
    cursor: pointer;
    opacity: 0.6;
    filter: brightness(0.8);
}

.rm_tag_filter .tag.actionable {
    transition: opacity 200ms;
}

.rm_tag_filter .tag:hover {
    opacity: 1;
    filter: brightness(1);
}

.tags_view {
    margin: 0;
    aspect-ratio: 1 / 1;
}

.tag.selected {
    opacity: 1 !important;
    filter: none !important;
}

.tag.excluded {
    opacity: 1 !important;
    filter: saturate(0.4) !important;
    border: 1px solid red;
}

.tag.excluded::after {
    position: absolute;
    height: calc(var(--mainFontSize)*1.5);
    left: 0;
    right: 0;
    content: "\d7";
    pointer-events: none;
    font-size: calc(var(--mainFontSize) *3);
    color: red;
    line-height: calc(var(--mainFontSize)*1.3);
    text-align: center;
    text-shadow: 1px 1px 0px black,
        -1px -1px 0px black,
        -1px 1px 0px black,
        1px -1px 0px black;
    opacity: 1;
}

.tag_as_folder.right_menu_button {
    filter: brightness(75%) saturate(0.6);
    margin-right: 5px;
}

.tag_as_folder.right_menu_button:hover,
.tag_as_folder.right_menu_button.flash {
    filter: brightness(150%) saturate(0.6);
}

.tag_as_folder.right_menu_button.no_folder {
    filter: brightness(25%) saturate(0.25);
}

.tag_as_folder.right_menu_button .tag_folder_indicator {
    position: absolute;
    top: calc(var(--mainFontSize) * -0.5);
    right: calc(var(--mainFontSize) * -0.5);
    font-size: calc(var(--mainFontSize) * 1);
    line-height: calc(var(--mainFontSize) * 1.3);
    text-align: center;
    text-shadow: 1px 1px 0px black,
        -1px -1px 0px black,
        -1px 1px 0px black,
        1px -1px 0px black;
    opacity: 1;
}

.tag.indicator::after {
    position: absolute;
    top: calc(var(--mainFontSize) * -0.5);
    right: -2px;
    content: "\25CF";
    font-size: calc(var(--mainFontSize) * 1);
    color: var(--SmartThemeBodyColor);
    line-height: calc(var(--mainFontSize) * 1.3);
    text-align: center;
    text-shadow: 1px 1px 0px black,
        -1px -1px 0px black,
        -1px 1px 0px black,
        1px -1px 0px black;
    opacity: 1;
}

.rm_tag_bogus_drilldown {
    height: calc(var(--mainFontSize)* 2 - 2);
}

.rm_tag_bogus_drilldown .tag:not(:first-child) {
    position: relative;
    margin-left: 1em;
}

.rm_tag_bogus_drilldown .tag:not(:first-child)::before {
    font-family: 'Font Awesome 6 Free';
    content: "\f054";
    position: absolute;
    left: -1em;
    top: auto;
    color: var(--SmartThemeBodyColor);
    text-shadow: 1px 1px 0px black,
        -1px -1px 0px black,
        -1px 1px 0px black,
        1px -1px 0px black;
    opacity: 1;
}

.bogus_folder_select_back .avatar {
    display: none !important;
}

.bogus_folder_select_back .bogus_folder_back_placeholder {
    min-height: calc(var(--mainFontSize)*2);
    width: var(--avatar-base-width);
    justify-content: center;
}
