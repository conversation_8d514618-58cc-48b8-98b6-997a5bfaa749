.file_attached {
    display: flex;
    min-width: 150px;
    max-width: calc(var(--sheldWidth) * 0.9);
    flex-direction: row;
    gap: 10px;
    align-items: center;
    margin: 0.25em auto;
    padding: 0 0.75em;
    border: 2px solid var(--SmartThemeBorderColor);
    border-radius: 15px;
    background-color: var(--white20a);
}

.mes_file_container {
    cursor: default;
    display: flex;
    gap: 15px;
    align-items: center;
    width: fit-content;
    max-width: 100%;
    background-color: var(--white20a);
    border: 2px solid var(--SmartThemeBorderColor);
    padding: 0.5em 1em;
    border-radius: 15px;
}

.mes_file_container .right_menu_button {
    padding-right: 0;
}

.mes_file_container .mes_file_size,
.file_attached .file_size {
    font-size: 0.9em;
    color: var(--SmartThemeQuoteColor);
}

.file_attached .file_name,
.mes_file_container .mes_file_name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#file_form {
    display: flex;
    width: 100%;
}

.file_modal {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    display: flex;
    text-align: left;
}
