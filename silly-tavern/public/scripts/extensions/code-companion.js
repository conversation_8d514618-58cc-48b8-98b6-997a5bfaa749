
// SOVEREIGN-AI Code Companion Extension for SillyTavern
class CodeCompanionExtension {
    constructor() {
        this.name = "Code Companion";
        this.version = "1.0.0";
        this.description = "AI-powered coding assistance integrated into chat";
    }

    init() {
        this.addCodeCompanionButton();
        this.setupCodeHighlighting();
        this.registerCodeCommands();
    }

    addCodeCompanionButton() {
        const toolbar = document.querySelector('#chat-toolbar');
        if (toolbar) {
            const codeBtn = document.createElement('button');
            codeBtn.innerHTML = '💻 Code';
            codeBtn.className = 'menu_button';
            codeBtn.onclick = () => this.openCodePanel();
            toolbar.appendChild(codeBtn);
        }
    }

    openCodePanel() {
        const codePanel = document.createElement('div');
        codePanel.innerHTML = `
            <div id="code-companion-panel" style="
                position: fixed; top: 10%; right: 10px;
                width: 400px; height: 500px;
                background: #1a1a1a; border: 2px solid #0066ff;
                border-radius: 10px; z-index: 1000; padding: 10px;
            ">
                <h3 style="color: #0066ff;">Code Companion</h3>
                <textarea id="code-input" placeholder="Enter your code here..."
                         style="width: 100%; height: 200px; background: #000; color: #00ff00; font-family: monospace;"></textarea>
                <br><br>
                <button onclick="codeCompanion.completeCode()">Complete Code</button>
                <button onclick="codeCompanion.explainCode()">Explain Code</button>
                <button onclick="codeCompanion.refactorCode()">Refactor Code</button>
                <br><br>
                <div id="code-output" style="background: #000; color: #00ff00; height: 150px; overflow-y: auto; padding: 5px; font-family: monospace;"></div>
                <button onclick="codeCompanion.closePanel()" style="float: right;">Close</button>
            </div>
        `;
        document.body.appendChild(codePanel);
    }

    async completeCode() {
        const code = document.getElementById('code-input').value;
        const response = await fetch('/api/code/complete', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({code: code, language: 'python'})
        });
        const result = await response.json();
        document.getElementById('code-output').innerHTML = result.completion;
    }

    closePanel() {
        const panel = document.getElementById('code-companion-panel');
        if (panel) panel.remove();
    }
}

// Initialize Code Companion
const codeCompanion = new CodeCompanionExtension();
codeCompanion.init();
