
// SOVEREIGN-AI Agent Zero Extension for SillyTavern
class AgentZeroExtension {
    constructor() {
        this.name = "Agent Zero";
        this.version = "1.0.0";
        this.description = "Multi-agent OS control integrated into chat";
    }

    init() {
        this.addAgentZeroButton();
        this.setupSystemCommands();
    }

    addAgentZeroButton() {
        const toolbar = document.querySelector('#chat-toolbar');
        if (toolbar) {
            const agentBtn = document.createElement('button');
            agentBtn.innerHTML = '🤖 Agents';
            agentBtn.className = 'menu_button';
            agentBtn.onclick = () => this.openAgentPanel();
            toolbar.appendChild(agentBtn);
        }
    }

    openAgentPanel() {
        const agentPanel = document.createElement('div');
        agentPanel.innerHTML = `
            <div id="agent-zero-panel" style="
                position: fixed; top: 10%; left: 10px;
                width: 400px; height: 500px;
                background: #1a1a1a; border: 2px solid #00aa00;
                border-radius: 10px; z-index: 1000; padding: 10px;
            ">
                <h3 style="color: #00aa00;">Agent Zero Control</h3>
                <input id="system-command" placeholder="Enter system command..."
                       style="width: 100%; background: #000; color: #00ff00; padding: 5px;">
                <br><br>
                <button onclick="agentZero.executeCommand()">Execute Command</button>
                <button onclick="agentZero.scanSystem()">Scan System</button>
                <button onclick="agentZero.deployAgents()">Deploy Agents</button>
                <br><br>
                <div id="agent-output" style="background: #000; color: #00ff00; height: 300px; overflow-y: auto; padding: 5px; font-family: monospace;"></div>
                <button onclick="agentZero.closePanel()" style="float: right;">Close</button>
            </div>
        `;
        document.body.appendChild(agentPanel);
    }

    async executeCommand() {
        const command = document.getElementById('system-command').value;
        const response = await fetch('/api/system/execute', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({command: command})
        });
        const result = await response.json();
        document.getElementById('agent-output').innerHTML += `> ${command}\n${result.result}\n\n`;
    }

    closePanel() {
        const panel = document.getElementById('agent-zero-panel');
        if (panel) panel.remove();
    }
}

// Initialize Agent Zero
const agentZero = new AgentZeroExtension();
agentZero.init();
