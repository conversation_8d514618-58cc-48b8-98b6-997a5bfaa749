How to Update SillyTavern

The most recent version can be found here: https://docs.sillytavern.app/usage/update/

This is not an installation guide. If you need installation instructions, look here:
https://docs.sillytavern.app/installation/windows/

This guide assumes you have already installed SillyTavern once, and know how to run it on your OS.

Linux/Termux:

You definitely installed via git, so just 'git pull' inside the SillyTavern directory.

Windows/MacOS:

Method 1 - GIT

We always recommend users install using 'git'. Here's why:

When you have installed via `git clone`, all you have to do to update is type `git pull` in a command line in the ST folder.
You can also try running the 'UpdateAndStart.bat' file, which will almost do the same thing. (Windows only)
Alternatively, if the command prompt gives you problems (and you have GitHub Desktop installed), you can use the 'Repository' menu and select 'Pull'.
The updates are applied automatically and safely.

If you are a developer and use a fork of ST or switch branches regularly, you can use the 'UpdateForkAndStart.bat', which works similarly to 'UpdateAndStart.bat',
but automatically pulls changes into your fork and handles switched branches gracefully by asking if you want to switch back.

Method 2 - ZIP

If you insist on installing via a zip, here is the tedious process for doing the update:

1. Download the new release zip.
2. Unzip it into a folder OUTSIDE of your current ST installation.
3. Do the usual setup procedure for your OS to install the NodeJS requirements.

4a. Updating 1.12.0 and above

Copy the user data directory from your data root into the data root of the new install.

By default: /data/default-user

4a. Migrating from <1.12.0 to >=1.20.0
Copy the following files/folders as necessary(*) from your old ST installation:

  - Assets
  - Backgrounds
  - Characters
  - Chats
  - Context
  - Groups
  - Group chats
  - Instruct
  - movingUI
  - KoboldAI Settings
  - NovelAI Settings
  - OpenAI Settings (Chat Completion API)
  - TextGen Settings (Text Completion API)
  - QuickReplies
  - Themes
  - User Avatars
  - Worlds
  - User
  - settings.json
  - secrets.json <---- This one is in the base folder, not /public/

  (*) 'As necessary' = "If you made any custom content related to those folders".
  None of the folders are mandatory, so only copy what you need.

  **NB: DO NOT COPY THE ENTIRE /PUBLIC/ FOLDER.**
  Doing so could break the new install and prevent new features from being present.
  Paste those items into the /data/default-user folder of the new install.

5. Start SillyTavern once again with the method appropriate to your OS, and pray you got it right.

6. If everything shows up, you can safely delete the old ST folder.
